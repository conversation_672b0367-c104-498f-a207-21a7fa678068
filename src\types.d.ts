// This allows TypeScript to pick up the magic constants that's auto-generated by Forge's Vite
// plugin that tells the Electron app where to look for the Vite-bundled app code (depending on
// whether you're running in development or production).
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string;
declare const MAIN_WINDOW_VITE_NAME: string;

// Preload types
interface ThemeModeContext {
  toggle: () => Promise<boolean>;
  dark: () => Promise<void>;
  light: () => Promise<void>;
  system: () => Promise<boolean>;
  current: () => Promise<"dark" | "light" | "system">;
}

interface ElectronWindow {
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
}

interface DatabaseAPI {
  getServiceLevelData: () => Promise<Array<{ datum: string; servicegrad: number }>>;
  getPickingData: () => Promise<Array<any>>;
  getReturnsData: () => Promise<Array<any>>;
  getDeliveryPositionsData: () => Promise<Array<any>>;
  getTagesleistungData: () => Promise<Array<any>>;
  getAblaengereiData: () => Promise<Array<any>>;
  getLagerCutsData: () => Promise<Array<any>>;
  getCuttingsData: () => Promise<Array<any>>;
  getWEData: () => Promise<Array<any>>;
  getSystemAtrlData: () => Promise<Array<any>>;
  getSystemArilData: () => Promise<Array<any>>;
}

interface ElectronAPI {
  database: DatabaseAPI;
}

declare interface Window {
  themeMode: ThemeModeContext;
  electronWindow: ElectronWindow;
  electronAPI: ElectronAPI;
}
