/**
 * Typdefinitionen für die Datenbank-API
 * 
 * Diese Definitionen ermöglichen den typisierten Zugriff auf die Datenbank
 * im Renderer-Prozess.
 */

/**
 * Servicelevel-Datenpunkt
 */
interface ServiceLevelDataPoint {
  datum: string;
  csr: number;
}

/**
 * Tägliche Leistungsdaten
 */
interface DailyPerformanceDataPoint {
  datum: string;
  value: number;
}

/**
 * Kommissionierungsdaten
 */
interface PickingDataPoint {
  name: string;
  value: number;
}

/**
 * Retourendaten
 */
interface ReturnsDataPoint {
  name: string;
  value: number;
}

/**
 * Lieferpositionsdaten
 */
interface DeliveryPositionsDataPoint {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

/**
 * Tagesleistungsdaten
 */
interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

/**
 * Ablaengerei-Datenpunkt
 */
interface AblaengereiDataPoint {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
  lagerCut220: number | null;
  lagerCut240: number | null;
  lagerCut200: number | null;
  cutLagerK200: number | null;
  cutLagerK240: number | null;
  cutLagerK220: number | null;
  cutLager200: number | null;
  cutLagerR240: number | null;
  cutLagerR220: number | null;
  
  // Alias-Felder für Kompatibilität
  lagerCut220?: number | null;
  lagerCut240?: number | null;
  lagerCut200?: number | null;
  cutLagerK200?: number | null;
  cutLagerK240?: number | null;
  cutLagerK220?: number | null;
  cutLager200?: number | null;
  cutLagerR240?: number | null;
  cutLagerR220?: number | null;
}

/**
 * WE-Datenpunkt (Wareneingang)
 */
interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;  // Entspricht atrl in dispatch_data
  weManl: number | null;  // Entspricht aril in dispatch_data
}

/**
 * Datenbank-API
 */
interface DatabaseAPI {
  /**
   * Ruft die Servicelevel-Daten für das Diagramm ab
   * @returns Promise mit den Servicelevel-Daten
   */
  getServiceLevelData: () => Promise<ServiceLevelDataPoint[]>;
  
  /**
   * Ruft die täglichen Leistungsdaten für das Diagramm ab
   * @returns Promise mit den täglichen Leistungsdaten
   */
  getDailyPerformanceData: () => Promise<DailyPerformanceDataPoint[]>;
  
  /**
   * Ruft die Kommissionierungsdaten für das Diagramm ab
   * @returns Promise mit den Kommissionierungsdaten
   */
  getPickingData: () => Promise<PickingDataPoint[]>;
  
  /**
   * Ruft die Retourendaten für das Diagramm ab
   * @returns Promise mit den Retourendaten
   */
  getReturnsData: () => Promise<ReturnsDataPoint[]>;
  
  /**
   * Ruft die Lieferpositionsdaten für das Diagramm ab
   * @returns Promise mit den Lieferpositionsdaten
   */
  getDeliveryPositionsData: () => Promise<DeliveryPositionsDataPoint[]>;
  
  /**
   * Ruft die Tagesleistungsdaten für das Diagramm ab
   * @returns Promise mit den Tagesleistungsdaten
   */
  getTagesleistungData: () => Promise<TagesleistungDataPoint[]>;
  
  /**
   * Importiert die Daten aus der Abl.csv-Datei in die Ablaengerei-Tabelle
   * @returns Promise mit dem Ergebnis des Imports (true/false)
   */
  importAblaengereiCsvData: () => Promise<boolean>;
  
  /**
   * Ruft die Daten aus der Ablaengerei-Tabelle ab
   * @returns Promise mit den Ablaengerei-Daten
   */
  getAblaengereiData: () => Promise<AblaengereiDataPoint[]>;
  
  /**
   * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
   * @returns Promise mit den WE-Daten
   */
  getWEData: () => Promise<WEDataPoint[]>;
}

/**
 * Erweiterung der Window-Schnittstelle um die Datenbank-API
 */
interface Window {
  database: DatabaseAPI;
}
