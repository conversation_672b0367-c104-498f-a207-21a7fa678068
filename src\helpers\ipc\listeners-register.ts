import { BrowserWindow, ipcMain } from "electron";
import { addThemeEventListeners } from "./theme/theme-listeners";
import { addWindowEventListeners } from "./window/window-listeners";
import {
  getServiceLevelData,
  getPickingData,
  getReturnsData,
  getDeliveryPositionsData,
  getTagesleistungData,
  getAblaengereiData,
  getLagerCutsData,
  getCuttingsData,
  getWEData,
  getSystemAtrlData
} from "./database-handlers";

/**
 * Registriert alle Event-Listener für die IPC-Kommunikation
 * @param mainWindow Das Hauptfenster der Anwendung
 */
export default function registerListeners(mainWindow: BrowserWindow) {
  // Registriere Fenster- und Theme-Listener
  addWindowEventListeners(mainWindow);
  addThemeEventListeners();
  
  // Registriere Datenbankhandler
  ipcMain.handle('get-service-level-data', getServiceLevelData);
  ipcMain.handle('get-picking-data', getPickingData);
  ipcMain.handle('get-returns-data', getReturnsData);
  ipcMain.handle('get-delivery-positions-data', getDeliveryPositionsData);
  ipcMain.handle('get-tagesleistung-data', getTagesleistungData);
  ipcMain.handle('get-ablaengerei-data', getAblaengereiData);
  ipcMain.handle('get-lager-cuts-data', getLagerCutsData);
  ipcMain.handle('get-cuttings-data', getCuttingsData);
  ipcMain.handle('get-we-data', getWEData);
  ipcMain.handle('get-system-atrl-data', getSystemAtrlData);
  
  console.log('Alle IPC-Handler wurden registriert.');
}
