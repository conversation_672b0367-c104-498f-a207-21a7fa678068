import React, { useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import { isWithinInterval, parseISO } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";;
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";

// Definition der Datentypen
interface LagerCutsDataPoint {
  name: string;
  date?: Date;
  lagerCut200: number;
  lagerCut240: number;
  lagerCut220: number; 
  cutLagerK220: number;
  cutLagerK240: number;
  cutLagerK200: number;
  cutLagerR220: number;
  cutLagerR240: number;
  cutLager200: number;
}

interface LagerCutsChartProps {
  dateRange?: DateRange;
}

export function LagerCutsChart({ dateRange }: LagerCutsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<LagerCutsDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    lagerSumme: {
      label: "Lager --> Ablaengerei",
      color: "#00C6CF",
    },
    cutLagerKSumme: {
      label: "Kunden Schnitte --> Lager",
      color: "#FF5470",
    },
    cutLagerRSumme: {
      label: "Rest Schnitte --> Lager",
      color: "#FFB800",
    }
  };

  // Berechne die Summen für jedes Chart-Datum
  const chartDataWithSums = chartData.map(item => ({
    ...item,
    lagerSumme: item.lagerCut200 + item.lagerCut240 + item.lagerCut220,
    cutLagerKSumme: item.cutLagerK220 + item.cutLagerK240 + item.cutLagerK200,
    cutLagerRSumme: item.cutLagerR220 + item.cutLagerR240 + item.cutLager200
  }));

  // Berechne Gesamtwerte und Verhältnisse für den Footer
  const totalLagerAbgang = chartDataWithSums.reduce((sum, item) => sum + item.lagerSumme, 0);
  const totalKundenZugang = chartDataWithSums.reduce((sum, item) => sum + item.cutLagerKSumme, 0);
  const totalRestZugang = chartDataWithSums.reduce((sum, item) => sum + item.cutLagerRSumme, 0);
  const totalLagerZugang = totalKundenZugang + totalRestZugang;
  
  // Verhältnis berechnen: Lagerzugang / Lagerabgang
  const verhaeltnis = totalLagerAbgang > 0 ? (totalLagerZugang / totalLagerAbgang) : 0;
  const verhaeltnisText = totalLagerAbgang > 0 ? `${verhaeltnis.toFixed(2)}:1` : 'N/A';
  
  // Durchschnittswerte
  const avgLagerAbgang = chartDataWithSums.length > 0 ? totalLagerAbgang / chartDataWithSums.length : 0;
  const avgLagerZugang = chartDataWithSums.length > 0 ? totalLagerZugang / chartDataWithSums.length : 0;

  // Lade Daten aus der Datenbank
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (!window.electronAPI?.database?.getAblaengereiData) {
          throw new Error('Datenbank-API ist nicht verfügbar');
        }
        
        console.log('Lade Lagerdaten für LagerCutsChart...');
        const result = await window.electronAPI.database.getAblaengereiData();
        
        if (!Array.isArray(result)) {
          throw new Error('Ungültiges Datenformat von der Datenbank erhalten');
        }
        
        console.log('Ablaengerei-Daten aus Datenbank erhalten:', result);
        
        if (result.length === 0) {
          console.warn('Keine Einträge in der Ablaengerei-Tabelle gefunden');
          setChartData([]);
          return;
        }
        
        // Zeige den ersten Datensatz zur Inspektion
        console.log('Erster Datensatz:', result[0]);
        console.log('Verfügbare Felder:', Object.keys(result[0]));
        
        // Konvertiere Ablaengerei-Daten zu Chart-Format und berechne Lagersummen
        let processedData = result.map((row) => {
          // Formatiere das Datum für die X-Achse
          let formattedDate = 'Unbekannt';
          let dateObj = new Date(0);
          
          if (row.datum) {
            try {
              dateObj = new Date(row.datum);
              formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
            } catch (e) {
              console.warn(`Fehler beim Formatieren des Datums ${row.datum}:`, e);
            }
          }
          
          // Berechne die Summen der Lager-Spalten
          const lagerSumme = (row.lagerCut200 || 0) + (row.lagerCut240 || 0) + (row.lagerCut220 || 0);
          const cutLagerKSumme = (row.cutLagerK220 || 0) + (row.cutLagerK240 || 0) + (row.cutLagerK200 || 0);
          const cutLagerRSumme = (row.cutLagerR220 || 0) + (row.cutLagerR240 || 0) + (row.cutLager200 || 0);
          
          console.log(`Datenpunkt erstellt: ${formattedDate}, Lager=${lagerSumme}, CutK=${cutLagerKSumme}, CutR=${cutLagerRSumme}`);
          
          return {
            name: formattedDate,
            date: dateObj,
            lagerCut200: row.lagerCut200 || 0,
            lagerCut240: row.lagerCut240 || 0,
            lagerCut220: row.lagerCut220 || 0,
            cutLagerK220: row.cutLagerK220 || 0,
            cutLagerK240: row.cutLagerK240 || 0,
            cutLagerK200: row.cutLagerK200 || 0,
            cutLagerR220: row.cutLagerR220 || 0,
            cutLagerR240: row.cutLagerR240 || 0,
            cutLager200: row.cutLager200 || 0
          };
        });
        
        // Sortiere die Daten nach Datum aufsteigend
        console.log('Sortiere Daten nach Datum...');
        processedData.sort((a, b) => {
          const dateA = a.date ? a.date.getTime() : 0;
          const dateB = b.date ? b.date.getTime() : 0;
          return dateA - dateB;
        });
        
        // Filtere nach Datumsbereich, falls vorhanden
        if (dateRange && dateRange.from && dateRange.to) {
          console.log(`Filtere Daten nach Datumsbereich: ${dateRange.from.toLocaleDateString()} bis ${dateRange.to.toLocaleDateString()}`);
          processedData = processedData.filter(item => {
            if (!item.date) return true;
            
            try {
              return isWithinInterval(item.date, {
                start: dateRange.from as Date,
                end: dateRange.to as Date
              });
            } catch (error) {
              console.error('Fehler beim Filtern nach Datum:', error);
              return true;
            }
          });
          console.log(`Nach Filterung verbleiben ${processedData.length} Datensätze`);
        }
        
        // Filtere Datensätze heraus, bei denen alle Lagerwerte 0 sind
        processedData = processedData.filter(item => {
          const lagerSumme = item.lagerCut200 + item.lagerCut240 + item.lagerCut220;
          const cutLagerKSumme = item.cutLagerK220 + item.cutLagerK240 + item.cutLagerK200;
          const cutLagerRSumme = item.cutLagerR220 + item.cutLagerR240 + item.cutLager200;
          return lagerSumme > 0 || cutLagerKSumme > 0 || cutLagerRSumme > 0;
        });
        console.log(`Nach Filterung von 0-Werten: ${processedData.length} Datensätze`);
        
        console.log('Finale Daten für das Chart:', processedData);
        setChartData(processedData);
      } catch (err) {
        console.error("Fehler beim Laden der Lagerdaten:", err);
        setError("Fehler beim Laden der Daten" + (err instanceof Error ? ": " + err.message : ""));
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateRange]); // dateRange als Dependency hinzugefügt

  // Debug-Ausgaben
  console.log('LagerCutsChart - chartData.length:', chartData.length);
  console.log('LagerCutsChart - dateRange:', dateRange);
  console.log('LagerCutsChart - loading:', loading);
  console.log('LagerCutsChart - error:', error);

  if (loading) return <div className="p-4 text-center">Lade Daten...</div>;
  if (error) return <div className="p-4 text-red-500">Fehler: {error}</div>;
  
  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black">
      <CardHeader>
        <CardTitle>LAGER-ABLÄNGEREI BEWEGUNGEN</CardTitle>
        <CardDescription>
          Bewegungen zwischen den Lägern und der Ablaengerei
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-60 w-full"
        >
          <BarChart data={chartDataWithSums} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="name" 
              className="text-xs font-bold"
              tickLine={false}
              axisLine={false}
              tickMargin={2}
              height={30} // Höhe für zweizeilige Darstellung
              tick={({x, y, payload}: {x: number, y: number, payload: any}) => {
                try {
                  // Sicherstellen, dass ein gültiger Wert vorhanden ist
                  if (!payload?.value) {
                    return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                  }
                  
                  // Extrahiere das Datum aus dem payload.value (Format: "DD.MM.YYYY")
                  const [day, month] = String(payload.value).split('.');
                  if (!day || !month) {
                    return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                  }
                  
                  const date = new Date();
                  date.setMonth(parseInt(month, 10) - 1);
                  date.setDate(parseInt(day, 10));
                  
                  // Wochentage auf Deutsch
                  const weekdays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
                  const weekday = weekdays[date.getDay()] || '-';
                  
                  return (
                    <g transform={`translate(${x},${y})`}>
                      <text x={0} y={0} dy={16} textAnchor="middle" fill="#000" className="text-[10px] sm:text-xs">
                        {`${day}.${month}`}
                      </text>
                      <text x={0} y={15} dy={16} textAnchor="middle" fill="#666" className="text-[9px] sm:text-[10px]">
                        {weekday}
                      </text>
                    </g>
                  );
                } catch (e) {
                  console.error('Fehler beim Rendern des X-Achsen-Labels:', e);
                  return <text x={0} y={0} textAnchor="middle" fill="#000">-</text>;
                }
              }}
            />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              // Beschriftung für die Y-Achse
              label={{ 
                value: "Anzahl Lager", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="lagerSumme"
              name="Lager --> Ablaengerei"
              fill={chartConfig.lagerSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="cutLagerKSumme"
              name="Kunden Schnitte --> Lager"
              fill={chartConfig.cutLagerKSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[0, 0, 0, 0]}
              className="neo-brutalism-bar"
              stackId="lager"
            />
            <Bar
              dataKey="cutLagerRSumme"
              name="Rest Schnitte --> Lager"
              fill={chartConfig.cutLagerRSumme.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="lager"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Zugang/Abgang-Verhältnis: {verhaeltnisText} | 
              Durchschnitt Zugang: {avgLagerZugang.toFixed(0)} | 
              Durchschnitt Abgang: {avgLagerAbgang.toFixed(0)}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt Zugang: {totalLagerZugang.toLocaleString()} | 
              Gesamt Abgang: {totalLagerAbgang.toLocaleString()} | 
              {chartData.length > 0 
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank` 
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
