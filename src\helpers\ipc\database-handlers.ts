import { IpcMainInvokeEvent } from 'electron';
import databaseService from '../../services/database';

/**
 * Handler für Datenbankabfragen über IPC
 * 
 * Diese Funktionen werden vom Renderer-Prozess aufgerufen, um Daten
 * aus der SQLite-Datenbank abzurufen oder zu importieren.
 */

/**
 * Ruft die Servicelevel-Daten für das Diagramm ab
 */
export const getServiceLevelData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getServiceLevelData();
};

/**
 * Ruft die Kommissionierungsdaten für das Diagramm ab
 */
export const getPickingData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getPickingData();
};

/**
 * Ruft die Retourendaten für das Diagramm ab
 */
export const getReturnsData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getReturnsData();
};

/**
 * Ruft die Lieferpositionsdaten für das Diagramm ab
 */
export const getDeliveryPositionsData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getDeliveryPositionsData();
};

/**
 * Ruft die Tagesleistungsdaten für das Diagramm ab
 */
export const getTagesleistungData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getTagesleistungData();
};

/**
 * Ruft die Daten aus der Ablaengerei-Tabelle ab
 */
export const getAblaengereiData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getAblaengereiData();
};

/**
 * Ruft die Lagerdaten für den LagerCutsChart ab
 */
export const getLagerCutsData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getLagerCutsData();
};

/**
 * Ruft die Schneidedaten für den CuttingsChart ab
 */
export const getCuttingsData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getCuttingsData();
};

/**
 * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
 */
export const getWEData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getWEData();
};

/**
 * Ruft die System-ATRL-Daten für das Diagramm ab
 */
export const getSystemAtrlData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getSystemAtrlData();
};

/**
 * Ruft die System-ARIL-Daten für das Diagramm ab
 */
export const getSystemArilData = async (_event: IpcMainInvokeEvent): Promise<any[]> => {
  return databaseService.getSystemArilData();
};
