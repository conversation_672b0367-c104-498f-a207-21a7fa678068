// Import Electron-Module mit ES6-Syntax
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * Stellt die Datenbankfunktionen im Renderer-Prozess zur Verfügung
 * 
 * Diese Funktionen ermöglichen den Zugriff auf die SQLite-Datenbank
 * über IPC-Kommunikation mit dem Hauptprozess.
 */
export function exposeDatabaseContext() {
  // Definiere die Datenbankfunktionen, die im Renderer-Prozess verfügbar sein sollen
  const databaseAPI = {
    /**
     * Ruft die Servicelevel-Daten für das Diagramm ab
     * @returns Promise mit den Servicelevel-Daten
     */
    getServiceLevelData: () => ipcRenderer.invoke('get-service-level-data'),
    
    /**
     * Ruft die Kommissionierungsdaten für das Diagramm ab
     * @returns Promise mit den Kommissionierungsdaten
     */
    getPickingData: () => ipcRenderer.invoke('get-picking-data'),
    
    /**
     * Ruft die Retourendaten für das Diagramm ab
     * @returns Promise mit den Retourendaten
     */
    getReturnsData: () => ipcRenderer.invoke('get-returns-data'),
    
    /**
     * Ruft die Lieferpositionsdaten für das Diagramm ab
     * @returns Promise mit den Lieferpositionsdaten
     */
    getDeliveryPositionsData: () => ipcRenderer.invoke('get-delivery-positions-data'),
    
    /**
     * Ruft die Tagesleistungsdaten für das Diagramm ab
     * @returns Promise mit den Tagesleistungsdaten
     */
    getTagesleistungData: () => ipcRenderer.invoke('get-tagesleistung-data'),
    
    /**
     * Ruft die Daten aus der Ablaengerei-Tabelle ab
     * @returns Promise mit den Ablaengerei-Daten
     */
    getAblaengereiData: () => ipcRenderer.invoke('get-ablaengerei-data'),
    
    /**
     * Ruft die Lagerdaten für den LagerCutsChart ab
     * @returns Promise mit den Lagerdaten
     */
    getLagerCutsData: () => ipcRenderer.invoke('get-lager-cuts-data'),
    
    /**
     * Ruft die Schneidedaten für den CuttingsChart ab
     * @returns Promise mit den Schneidedaten
     */
    getCuttingsData: () => ipcRenderer.invoke('get-cuttings-data'),
    
    /**
     * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
     * @returns Promise mit den WE-Daten
     */
    getWEData: () => ipcRenderer.invoke('get-we-data'),

    /**
     * Ruft die System-ATRL-Daten für das Diagramm ab
     * @returns Promise mit den System-ATRL-Daten
     */
    getSystemAtrlData: () => ipcRenderer.invoke('get-system-atrl-data'),

    /**
     * Ruft die System-ARIL-Daten für das Diagramm ab
     * @returns Promise mit den System-ARIL-Daten
     */
    getSystemArilData: () => ipcRenderer.invoke('get-system-aril-data')
  };
  
  // Stelle die Datenbankfunktionen im Renderer-Prozess zur Verfügung
  // Für Kompatibilität mit bestehenden Charts verwenden wir electronAPI.database
  contextBridge.exposeInMainWorld('electronAPI', {
    database: databaseAPI
  });
  
  console.log('Datenbank-API wurde im Renderer-Prozess verfügbar gemacht.');
}
