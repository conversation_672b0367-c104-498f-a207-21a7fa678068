// Skript zum Überprüfen der Ablaengerei-Tabelle
const Database = require('better-sqlite3');
const path = require('path');

// Pfad zur Datenbank
const dbPath = path.join(__dirname, 'sfm_dashboard.db');

try {
  // Verbinde zur Datenbank
  const db = new Database(dbPath);
  
  console.log('Datenbank gefunden:', dbPath);
  
  // Prüfe, ob die Ablaengerei-Tabelle existiert
  const tableExists = db.prepare(`
    SELECT name FROM sqlite_master WHERE type='table' AND name='Ablaengerei'
  `).get();
  
  if (!tableExists) {
    console.log('Tabelle "Ablaengerei" existiert nicht!');
    
    // Zeige alle verfügbaren Tabellen
    const allTables = db.prepare(`
      SELECT name FROM sqlite_master WHERE type='table'
    `).all();
    
    console.log('Verfügbare Tabellen:');
    allTables.forEach(table => {
      console.log(`- ${table.name}`);
    });
  } else {
    console.log('Tabelle "Ablaengerei" gefunden!');
    
    // Hole die Tabellenstruktur
    const tableInfo = db.prepare("PRAGMA table_info(Ablaengerei)").all();
    
    console.log("\nStruktur der Tabelle 'Ablaengerei':");
    tableInfo.forEach(column => {
      console.log(`${column.cid}: ${column.name} (${column.type}) ${column.notnull ? 'NOT NULL' : ''} ${column.pk ? 'PRIMARY KEY' : ''}`);
    });
    
    // Prüfe die Anzahl der Datensätze
    const countResult = db.prepare("SELECT COUNT(*) as count FROM Ablaengerei").get();
    console.log(`\nAnzahl der Datensätze in der Tabelle 'Ablaengerei': ${countResult.count}`);
    
    if (countResult.count > 0) {
      // Zeige die ersten 3 Datensätze
      const sampleData = db.prepare("SELECT * FROM Ablaengerei LIMIT 3").all();
      console.log('\nDie ersten 3 Datensätze:');
      sampleData.forEach((row, index) => {
        console.log(`\n--- Datensatz ${index + 1} ---`);
        Object.entries(row).forEach(([key, value]) => {
          console.log(`${key}: ${value}`);
        });
      });
    }
  }
  
  // Schließe die Datenbankverbindung
  db.close();
  
  console.log('\nDatenbanküberprüfung abgeschlossen.');
  
} catch (error) {
  console.error('Fehler beim Überprüfen der Datenbank:', error);
} 