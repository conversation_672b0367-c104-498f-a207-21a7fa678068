import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import AreaChartStacked from "@/components/charts/ServiceGradChart";
import { DailyPerformanceChart } from "@/components/charts/LieferpositionenChart";
import { PickingChart } from "@/components/charts/PickingChart";
import { ReturnsChart } from "@/components/charts/QMeldungenChart";
import { TagesleistungChart } from "@/components/charts/TagesleistungChart";
import { CuttingsChart } from "@/components/charts/CuttingsChart";
import { IncomingPositionsChart } from "@/components/charts/IncomingPositionsChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";

/**
 * Dispatch-Dashboard mit sauberen, wiederverwendbaren Komponenten
 * 
 * Alle zeitbasierten Charts werden automatisch basierend auf dem 
 * ausgewählten Datumsbereich gefiltert. Die Charts reagieren 
 * auf Änderungen des dateRange-States und aktualisieren ihre Daten entsprechend.
 */
export default function DispatchPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // 1. Januar dieses Jahr
    to: addDays(new Date(new Date().getFullYear(), 0, 1), 10), // 30 Tage später
  });

  return ( 
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black">VERSAND</h1>
        <DateRangePicker 
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          <div className="xl:col-span-1">
            <AreaChartStacked dateRange={dateRange} />
          </div>

          <div className="xl:col-span-2">
            <TagesleistungChart dateRange={dateRange} />
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-6">
          <div className="xl:col-span-1">
            <DailyPerformanceChart dateRange={dateRange} />
          </div>
            
          <div className="xl:col-span-1">
            <ReturnsChart dateRange={dateRange} />
          </div>
            
          <div className="xl:col-span-1">
            <PickingChart dateRange={dateRange} />
          </div>
        </div>
      </div>
    </div>    

  )
};
