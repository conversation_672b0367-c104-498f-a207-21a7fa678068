import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SystemAtrlDataPoint } from "@/services/database";

interface SystemAtrlChartProps {
  dateRange?: DateRange;
}

export function SystemAtrlChart({ dateRange }: SystemAtrlChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<SystemAtrlDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const chartConfig = {
    ninioSapAtrl: { label: t("SAP"), color: "#8884d8" },
    nioWitronAtrl: { label: t("Witron"), color: "#82ca9d" },
    nioSiemensAtrl: { label: t("Siemens"), color: "#ffc658" },
    nioProzessAtrl: { label: t("Prozess"), color: "#ff8042" },
    nioSonstigesAtrl: { label: t("Sonstiges"), color: "#0088FE" },
  };

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("SystemAtrlChart: Starte Datenladung...");

      if (!window.electronAPI?.database?.getSystemAtrlData) {
        console.error("SystemAtrlChart: Datenbank-API nicht verfügbar");
        setError("Datenbank-API nicht verfügbar");
        return;
      }

      console.log("SystemAtrlChart: Rufe getSystemAtrlData auf...");
      const result = await window.electronAPI.database.getSystemAtrlData();
      console.log("SystemAtrlChart: Erhaltene Daten:", result);

      if (result && Array.isArray(result)) {
        console.log(`SystemAtrlChart: ${result.length} Datensätze erhalten`);

        // Filtere Daten basierend auf dem ausgewählten Datumsbereich
        let filteredData = result;
        if (dateRange?.from || dateRange?.to) {
          console.log("SystemAtrlChart: Filtere Daten nach Datumsbereich:", dateRange);
          filteredData = result.filter((item) => {
            try {
              // Konvertiere das Datum aus dem deutschen Format zurück zu einem Date-Objekt
              const dateParts = item.Datum.split('.');
              let itemDate: Date;

              if (dateParts.length === 3) {
                // Format: DD.MM.YYYY
                itemDate = new Date(parseInt(dateParts[2]), parseInt(dateParts[1]) - 1, parseInt(dateParts[0]));
              } else {
                // Fallback: versuche direktes Parsing
                itemDate = new Date(item.Datum);
              }

              if (dateRange?.from && itemDate < dateRange.from) return false;
              if (dateRange?.to && itemDate > dateRange.to) return false;

              return true;
            } catch (e) {
              console.error('SystemAtrlChart: Fehler bei der Datumsfilterung:', e, 'für Datum:', item.Datum);
              return true; // Bei Fehlern beim Datumsvergleich den Datenpunkt behalten
            }
          });
          console.log(`SystemAtrlChart: Nach Filterung ${filteredData.length} Datensätze übrig`);
        }

        setChartData(filteredData);
      } else {
        console.error("SystemAtrlChart: Keine gültigen Daten erhalten:", result);
        throw new Error("Keine gültigen Daten erhalten");
      }
    } catch (error) {
      console.error("SystemAtrlChart: Fehler beim Laden der Daten:", error);
      setError("Fehler beim Laden der Daten");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>{t("loading")}...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("systemAtrlTitle")}</CardTitle>
        <CardDescription>{t("systemAtrlDescription")}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-60 w-full">
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="Datum" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar dataKey="ninioSapAtrl" fill={chartConfig.ninioSapAtrl.color} name={chartConfig.ninioSapAtrl.label} />
            <Bar dataKey="nioWitronAtrl" fill={chartConfig.nioWitronAtrl.color} name={chartConfig.nioWitronAtrl.label} />
            <Bar dataKey="nioSiemensAtrl" fill={chartConfig.nioSiemensAtrl.color} name={chartConfig.nioSiemensAtrl.label} />
            <Bar dataKey="nioProzessAtrl" fill={chartConfig.nioProzessAtrl.color} name={chartConfig.nioProzessAtrl.label} />
            <Bar dataKey="nioSonstigesAtrl" fill={chartConfig.nioSonstigesAtrl.color} name={chartConfig.nioSonstigesAtrl.label} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
