// Skript zum Überprüfen der Lagerdaten in der Ablaengerei-Tabelle
const Database = require('better-sqlite3');
const path = require('path');

// Pfad zur Datenbank
const dbPath = path.join(__dirname, 'sfm_dashboard.db');

try {
  // Verbinde zur Datenbank
  const db = new Database(dbPath);
  
  console.log('Überprüfe Lagerdaten in der Ablaengerei-Tabelle...\n');
  
  // Prüfe Daten mit Lagerwerten
  const stmt = db.prepare(`
    SELECT 
      Datum,
      lagerCut200, lagerCut220, lagerCut240,
      cutLagerK200, cutLagerK220, cutLagerK240,
      cutLagerR220, cutLagerR240, cutLager200
    FROM Ablaengerei 
    WHERE (lagerCut200 IS NOT NULL AND lagerCut200 > 0) 
       OR (lagerCut220 IS NOT NULL AND lagerCut220 > 0)
       OR (lagerCut240 IS NOT NULL AND lagerCut240 > 0)
       OR (cutLagerK200 IS NOT NULL AND cutLagerK200 > 0)
       OR (cutLagerK220 IS NOT NULL AND cutLagerK220 > 0)
       OR (cutLagerK240 IS NOT NULL AND cutLagerK240 > 0)
    ORDER BY Datum DESC
    LIMIT 10
  `);

  const results = stmt.all();
  console.log(`Datensätze mit Lagerwerten gefunden: ${results.length}`);
  
  if (results.length > 0) {
    console.log('\nDie neuesten 10 Datensätze mit Lagerwerten:');
    results.forEach((row, i) => {
      console.log(`\n${i+1}. Datum: ${row.Datum}`);
      console.log(`   lagerCut200: ${row.lagerCut200}`);
      console.log(`   lagerCut220: ${row.lagerCut220}`);
      console.log(`   lagerCut240: ${row.lagerCut240}`);
      console.log(`   cutLagerK200: ${row.cutLagerK200}`);
      console.log(`   cutLagerK220: ${row.cutLagerK220}`);
      console.log(`   cutLagerK240: ${row.cutLagerK240}`);
    });
  }
  
  // Prüfe auch die Gesamtanzahl
  const totalCount = db.prepare(`
    SELECT COUNT(*) as count 
    FROM Ablaengerei 
    WHERE (lagerCut200 IS NOT NULL AND lagerCut200 > 0) 
       OR (lagerCut220 IS NOT NULL AND lagerCut220 > 0)
       OR (lagerCut240 IS NOT NULL AND lagerCut240 > 0)
       OR (cutLagerK200 IS NOT NULL AND cutLagerK200 > 0)
       OR (cutLagerK220 IS NOT NULL AND cutLagerK220 > 0)
       OR (cutLagerK240 IS NOT NULL AND cutLagerK240 > 0)
  `).get();
  
  console.log(`\nGesamtanzahl der Datensätze mit Lagerwerten: ${totalCount.count}`);
  
  // Prüfe den Datumsbereich der Lagerdaten
  const dateRange = db.prepare(`
    SELECT 
      MIN(Datum) as minDate,
      MAX(Datum) as maxDate
    FROM Ablaengerei 
    WHERE (lagerCut200 IS NOT NULL AND lagerCut200 > 0) 
       OR (lagerCut220 IS NOT NULL AND lagerCut220 > 0)
       OR (lagerCut240 IS NOT NULL AND lagerCut240 > 0)
       OR (cutLagerK200 IS NOT NULL AND cutLagerK200 > 0)
       OR (cutLagerK220 IS NOT NULL AND cutLagerK220 > 0)
       OR (cutLagerK240 IS NOT NULL AND cutLagerK240 > 0)
  `).get();
  
  console.log(`\nDatumsbereich der Lagerdaten:`);
  console.log(`Von: ${dateRange.minDate}`);
  console.log(`Bis: ${dateRange.maxDate}`);
  
  // Schließe die Datenbankverbindung
  db.close();
  
  console.log('\nÜberprüfung abgeschlossen.');
  
} catch (error) {
  console.error('Fehler beim Überprüfen der Lagerdaten:', error);
} 