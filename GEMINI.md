# Gemini Code-Richtlinien für SFM Electron

Dieses Dokument beschreibt die wichtigsten Konventionen, Technologien und Vorgehensweisen für die Arbeit an der SFM Electron App.

## 1. Projektübersicht

- **Product Name:** electron-shadcn Template
- **Description:** Electron Forge mit shadcn-ui (Vite + Typescript)
- **Version:** 1.0.0
- **Lizenz:** MIT

## 2. Wichtige Technologien

- **Framework:** Electron
- **UI:** React, shadcn-ui, Tailwind CSS
- **Sprache:** TypeScript
- **Build-Tool:** Vite
- **Datenbank:** better-sqlite3
- **Routing:** TanStack Router
- **Testing:** Vites<PERSON>, Playwright

## 3. Wichtige Skripte

- **`npm start`**: Startet die Electron-Anwendung im Entwicklungsmodus.
- **`npm run lint`**: Überprüft den Code auf Stil- und Syntaxfehler mit ESLint.
- **`npm run format`**: Überprüft die Formatierung mit Prettier.
- **`npm run format:write`**: Formatiert den gesamten Code mit Prettier.
- **`npm test`**: Führt die Unit-Tests mit Vitest aus.
- **`npm run test:e2e`**: Führt die End-to-End-Tests mit Playwright aus.
- **`npm run generate:routes`**: Generiert die Routen mit dem TanStack Router CLI.

## 4. Code-Stil und Konventionen

- **Formatierung:** Prettier wird zur automatischen Code-Formatierung verwendet. Führen Sie `npm run format:write` aus, um sicherzustellen, dass Ihr Code den Stilrichtlinien entspricht.
- **Linting:** ESLint wird verwendet, um Code-Qualität und potenzielle Fehler sicherzustellen. Führen Sie `npm run lint` aus, um Probleme zu identifizieren.
- **TypeScript:** Das Projekt verwendet durchgehend TypeScript. Achten Sie auf starke Typisierung und vermeiden Sie `any`, wo immer möglich.
- **UI-Komponenten:** Neue UI-Komponenten sollten mit shadcn-ui und Tailwind CSS erstellt werden, um ein konsistentes Design zu gewährleisten.

## 5. Dateistruktur

- **`src/`**: Enthält den gesamten Quellcode der Anwendung.
  - **`main.ts`**: Hauptprozess von Electron.
  - **`renderer.tsx`**: UI-bezogener Code (React).
  - **`components/`**: Wiederverwendbare React-Komponenten.
  - **`pages/`**: Hauptansichten der Anwendung.
  - **`lib/`**: Hilfsfunktionen und Dienstprogramme.
  - **`database/`**: Skripte und Konfigurationen für die Datenbank.
- **`GEMINI.md`**: Dieses Dokument. Enthält wichtige Projektinformationen für die KI-gestützte Entwicklung.

## 6. Commit-Stil

- **Konventionelle Commits:** Bitte verwenden Sie das [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) Format für alle Commit-Nachrichten. Dies hilft, den Verlauf lesbar zu halten und die Versionierung zu automatisieren.
  - **Beispiele:**
    - `feat: Neue Chart-Komponente für die Tagesleistung hinzugefügt`
    - `fix: Fehler bei der Datumsberechnung im Dashboard behoben`
    - `docs: Anweisungen in der README aktualisiert`
    - `refactor: Authentifizierungslogik in einen eigenen Service ausgelagert`
