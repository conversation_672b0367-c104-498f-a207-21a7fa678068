// Skript zum Überprüfen der Datumsbereiche verschiedener Datentypen
const Database = require('better-sqlite3');
const path = require('path');

// Pfad zur Datenbank
const dbPath = path.join(__dirname, 'sfm_dashboard.db');

try {
  // Verbinde zur Datenbank
  const db = new Database(dbPath);
  
  console.log('Überprüfe Datumsbereiche der verschiedenen Datentypen...\n');
  
  // Prüfe den Datumsbereich aller Daten
  const dateRange = db.prepare('SELECT MIN(Datum) as minDate, MAX(Datum) as maxDate FROM Ablaengerei').get();
  console.log('Gesamter Datumsbereich:', dateRange);
  
  // Prüfe den Datumsbereich der Lagerdaten (nicht NULL)
  const lagerDateRange = db.prepare(`
    SELECT MIN(Datum) as minDate, MAX(Datum) as maxDate 
    FROM Ablaengerei 
    WHERE lagerCut200 IS NOT NULL OR lagerCut220 IS NOT NULL OR lagerCut240 IS NOT NULL
  `).get();
  console.log('Datumsbereich der Lagerdaten:', lagerDateRange);
  
  // Prüfe den Datumsbereich der Cut-Daten (nicht NULL)
  const cutDateRange = db.prepare(`
    SELECT MIN(Datum) as minDate, MAX(Datum) as maxDate 
    FROM Ablaengerei 
    WHERE cutTT IS NOT NULL OR cutTR IS NOT NULL OR cutRR IS NOT NULL
  `).get();
  console.log('Datumsbereich der Cut-Daten:', cutDateRange);
  
  // Prüfe Anzahl der Datensätze pro Jahr mit Lagerdaten
  console.log('\n=== Lagerdaten pro Jahr ===');
  const lagerPerYear = db.prepare(`
    SELECT 
      strftime('%Y', Datum) as jahr,
      COUNT(*) as anzahl
    FROM Ablaengerei 
    WHERE lagerCut200 IS NOT NULL OR lagerCut220 IS NOT NULL OR lagerCut240 IS NOT NULL
    GROUP BY strftime('%Y', Datum)
    ORDER BY jahr DESC
  `).all();
  
  lagerPerYear.forEach(row => {
    console.log(`Jahr ${row.jahr}: ${row.anzahl} Datensätze mit Lagerdaten`);
  });
  
  // Prüfe Anzahl der Datensätze pro Jahr mit Cut-Daten
  console.log('\n=== Cut-Daten pro Jahr ===');
  const cutPerYear = db.prepare(`
    SELECT 
      strftime('%Y', Datum) as jahr,
      COUNT(*) as anzahl
    FROM Ablaengerei 
    WHERE cutTT IS NOT NULL OR cutTR IS NOT NULL OR cutRR IS NOT NULL
    GROUP BY strftime('%Y', Datum)
    ORDER BY jahr DESC
  `).all();
  
  cutPerYear.forEach(row => {
    console.log(`Jahr ${row.jahr}: ${row.anzahl} Datensätze mit Cut-Daten`);
  });
  
  // Schließe die Datenbankverbindung
  db.close();
  
  console.log('\nÜberprüfung abgeschlossen.');
  
} catch (error) {
  console.error('Fehler beim Überprüfen der Datumsbereiche:', error);
} 