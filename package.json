{"name": "electron-shadcn", "productName": "electron-shadcn Template", "version": "1.0.0", "description": "Electron Forge with shadcn-ui (Vite + Typescript)", "main": ".vite/build/main.js", "private": true, "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint .", "format": "prettier --check .", "format:write": "prettier --write .", "test": "vitest run", "test:watch": "vitest watch", "test:unit": "vitest", "test:e2e": "playwright test", "test:all": "vitest run && playwright test", "generate:routes": "tsx node_modules/@tanstack/router-cli/cli.ts generate"}, "author": "ROG <<EMAIL>>", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron-forge/shared-types": "^7.8.0", "@electron/fuses": "^1.8.0", "@eslint/compat": "^1.2.8", "@eslint/js": "^9.25.1", "@playwright/test": "^1.52.0", "@stagewise/toolbar": "^0.4.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/router-cli": "^1.121.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/better-sqlite3": "^7.6.13", "@types/electron-squirrel-startup": "^1.0.2", "@types/eslint-config-prettier": "^6.11.3", "@types/node": "^22.15.19", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "electron": "^35.2.1", "electron-devtools-installer": "^4.0.0", "electron-playwright-helpers": "^1.7.1", "electron-rebuild": "^3.2.9", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "globals": "^16.0.0", "jsdom": "^26.0.0", "path-browserify": "^1.0.1", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "ts-prune": "^0.10.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.5", "vitest": "^3.1.2"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/babel-plugin": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@icons-pack/react-simple-icons": "^12.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.7", "@tanstack/react-router": "^1.121.2", "@tanstack/react-table": "^8.21.3", "@tanstack/router-devtools": "^1.117.1", "@types/sqlite3": "^3.1.11", "animejs": "^4.0.2", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "electron-squirrel-startup": "^1.0.1", "embla-carousel-react": "^8.6.0", "i18next": "^25.0.1", "lucide-react": "^0.294.0", "next-themes": "^0.4.6", "react": "18.2.0", "react-day-picker": "^9.7.0", "react-dom": "18.2.0", "react-i18next": "^15.5.1", "recharts": "^2.15.3", "sonner": "^2.0.5", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.2.9", "vaul": "^1.1.2", "zod": "^3.25.62"}}