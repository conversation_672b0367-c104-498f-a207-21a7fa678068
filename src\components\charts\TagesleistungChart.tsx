import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>hart,
  CartesianGrid,
  Composed<PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { useTranslation } from "react-i18next";
import { formatDateWithWeekday } from "@/lib/date-utils";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { cn } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

/**
 * TagesleistungChart Komponente
 * 
 * Zeigt Produktionsdaten als Balkendiagramm mit produzierten Tonnagen, Direktverladung KIAA, 
 * Umschlag, kg pro Colli und Elefanten an.
 * 
 * @param data Optional: <PERSON><PERSON><PERSON> von <PERSON> mit Produktionsdaten
 * @param dateRange Optional: Datumsbereich für die Filterung der Daten
 */

// Definition des Datentyps für die Produktionsdaten
interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

interface TagesleistungChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: TagesleistungDataPoint[];
  dateRange?: DateRange;
}

export function TagesleistungChart({ data: propData, dateRange }: TagesleistungChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<TagesleistungDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string>(new Date().toLocaleDateString());

  // Konfiguration für das Diagramm mit CSS-Variablen
  const chartConfig = {
    produzierte_tonnagen: {
      label: t("produzierteTonnagen"),
      color: "var(--chart-1)",
    },
    direktverladung_kiaa: {
      label: t("direktverladungKIAA"),
      color: "var(--chart-3)",
    },
    umschlag: {
      label: t("umschlag"),
      color: "var(--chart-4)",
    },
    kg_pro_colli: {
      label: t("kgProColli"),
      color: "var(--chart-2)",
    },
    elefanten: {
      label: t("elefanten"),
      color: "var(--chart-5)",
    },
  };
  
  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      setChartData(propData);
    } else {
      // Lade Daten aus der Datenbank
      const loadData = async () => {
        try {
          setLoading(true);
          setError(null);
          
          console.log('Versuche Produktionsdaten zu laden...');
          
          // Daten aus der Datenbank laden
          const result = await window.electronAPI?.database?.getTagesleistungData?.();
          console.log('Rohdaten aus Datenbank:', result);
          
          if (result && Array.isArray(result) && result.length > 0) {
            console.log('Produktionsdaten aus Datenbank erhalten:', result);
            
            // Wandle Datenbankzeilen in das richtige Format um
            const processedData: TagesleistungDataPoint[] = result.map((row: DatabaseRow) => ({
              date: row.date,
              produzierte_tonnagen: Number(row.produzierte_tonnagen) || 0,
              direktverladung_kiaa: Number(row.direktverladung_kiaa) || 0,
              umschlag: Number(row.umschlag) || 0,
              kg_pro_colli: Number(row.kg_pro_colli) || 0,
              elefanten: Number(row.elefanten) || 0
            }));
            
            // Filtere Daten basierend auf dem ausgewählten Datumsbereich
            let filteredData = processedData;
            if (dateRange?.from || dateRange?.to) {
              filteredData = processedData.filter((item) => {
                try {
                  const itemDate = new Date(item.date);
                  
                  if (dateRange?.from && itemDate < dateRange.from) return false;
                  if (dateRange?.to && itemDate > dateRange.to) return false;
                  
                  return true;
                } catch (e) {
                  console.error('Fehler bei der Datumsfilterung:', e);
                  return true; // Bei Fehlern beim Datumsvergleich den Datenpunkt behalten
                }
              });
            }
            
            setChartData(filteredData);
            setLastUpdate(new Date().toLocaleDateString());
          } else {
            console.warn('Keine Daten aus der Datenbank erhalten');
            setChartData([]);
          }
        } catch (err) {
          console.error('Fehler beim Laden der Produktionsdaten:', err);
          setError('Fehler beim Laden der Produktionsdaten');
          setChartData([]);
        } finally {
          setLoading(false);
        }
      };
      
      loadData();
    }
  }, [propData, dateRange]); // dateRange als Abhängigkeit hinzugefügt

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-500">
        <p className="font-bold">{t("error")}</p>
        <p>{error}</p>
      </div>
    );
  }

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105">
      <CardHeader>
        <CardTitle>TAGESLEISTUNG</CardTitle>
        <CardDescription>
          Produzierte Tonnagen, Direktverladung KIAA, Umschlag, kg pro Colli und Elefanten
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full h-60">
          <ComposedChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              height={40} // Erhöht für zweizeilige Darstellung
              tick={props => {
                const { x, y, payload } = props;
                try {
                  const date = new Date(payload.value);
                  // Wochentage auf Deutsch
                  const weekdays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
                  const weekday = weekdays[date.getDay()];
                  // Datum im Format "TT.MM."
                  const dateStr = date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                  
                  return (
                    <g transform={`translate(${x},${y})`}>
                      <text x={0} y={0} dy={0} textAnchor="middle" fontSize={12} fill="currentColor">
                        {dateStr}
                      </text>
                      <text x={0} y={0} dy={16} textAnchor="middle" fontSize={12} fill="currentColor">
                        {weekday}
                      </text>
                    </g>
                  );
                } catch {
                  return (
                    <text x={props.x} y={props.y} dy={props.dy} textAnchor="middle" fontSize={12} fill="currentColor">
                      {payload.value}
                    </text>
                  );
                }
              }}
            />
            <YAxis 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              tick={{ fontSize: 12 }}
              width={35}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Formatiere das Datum im Tooltip als DD.MM.YYYY
                    try {
                      const date = new Date(label);
                      return `${t("date")}: ${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
                    } catch {
                      return `${t("date")}: ${label}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            <Bar 
              dataKey="produzierte_tonnagen" 
              name={t("Produzierte Tonnagen")} 
              fill="var(--color-produzierte_tonnagen)" 
              radius={4} 
            />
            <Bar 
              dataKey="direktverladung_kiaa" 
              name={t("Direktverladung + KIAA")} 
              fill="var(--color-direktverladung_kiaa)" 
              radius={4} 
            />
            <Bar 
              dataKey="umschlag" 
              name={t("Umschlag")} 
              fill="var(--color-umschlag)" 
              radius={4} 
            />
            <Bar 
              dataKey="kg_pro_colli" 
              name={t("kg Pro Colli")} 
              fill="var(--color-kg_pro_colli)" 
              radius={4} 
            />
            <Bar 
              dataKey="elefanten" 
              name={t("Elefanten")} 
              fill="var(--color-elefanten)" 
              radius={4} 
            />
          </ComposedChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Produktionsdaten aus der Datenbank
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Letzte Aktualisierung: {lastUpdate}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}