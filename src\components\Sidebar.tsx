import * as React from "react"
import { <PERSON>, useRouter } from "@tanstack/react-router"
import { useTranslation } from "react-i18next"
import { Home, Truck, Scissors, PackageCheck, PanelLeft, PanelLeftClose, Settings, Drum, Shell, Activity } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useSidebar } from "@/components/ui/sidebar"

/**
 * Sidebar-Komponente für die Navigation zwischen den verschiedenen Abteilungen
 * 
 * Diese Komponente zeigt eine Seitenleiste mit Links zu den verschiedenen Abteilungen:
 * - Dispatch
 * - Cutting
 * - Incoming Goods
 * - Settings
 * 
 * Die Seitenleiste ist im Neobrutalism-Stil gestaltet mit kräftigen Farben und starken Konturen.
 */
export default function Sidebar() {
  const { t } = useTranslation()
  const { state, toggleSidebar } = useSidebar()
  const router = useRouter()
  const isOpen = state === 'expanded'
  const [currentPath, setCurrentPath] = React.useState(window.location.pathname)
  // Typdefinition für Navigationselemente
  interface NavItem {
    name: string
    icon: React.ReactNode
    to: string
    exact?: boolean
  }

  // Navigationselemente
  const navItems: NavItem[] = [
    {
      name: t("Dashboard"),
      icon: <Home size={20} />,
      to: "/",
      exact: true
    },
    {
      name: t("dispatchArea"),
      icon: <Truck size={20} />,
      to: "/dispatch"
    },
    {
      name: t("cuttingArea"),
      icon: <Scissors size={20} />,
      to: "/cutting"
    },
    {
      name: t("incomingGoodsArea"),
      icon: <PackageCheck size={20} />,
      to: "/incoming-goods"
    },
    {
      name: t("ATrL"),
      icon: <Drum size={20} />,
      to: "/atrl"
    },
    {
      name: t("ARiL"),
      icon: <Shell size={20} />,
      to: "/aril"
    },
    {
      name: t("System"),
      icon: <Activity size={20} />,
      to: "/system"
    },
  ]
  
  // Einstellungen-Menüpunkt (separat von den Hauptmenüpunkten)
  const settingsItem: NavItem = {
    name: "Einstellungen",
    icon: <Settings size={20} />,
    to: "/settings"
  }
  
  // Effekt zum Aktualisieren des aktuellen Pfads
  React.useEffect(() => {
    // Initialen Pfad setzen
    setCurrentPath(window.location.pathname)
    
    // Listener für Routenänderungen
    const unsubscribe = router.subscribe("onResolved", () => {
      setCurrentPath(window.location.pathname)
    })
    
    return () => {
      unsubscribe()
    }
  }, [router])
  
  // Funktion zur Überprüfung des aktiven Links
  const isLinkActive = React.useCallback((to: string, exact: boolean = false): boolean => {
    try {
      // Normalisiere die Pfade für den Vergleich
      const normalizedCurrent = currentPath.endsWith('/') ? currentPath.slice(0, -1) : currentPath
      const normalizedTo = to.endsWith('/') ? to.slice(0, -1) : to
      
      if (exact) {
        return normalizedCurrent === normalizedTo
      }
      
      // Berücksichtige auch Unterpfade für die Navigation
      return normalizedCurrent === normalizedTo || 
             (to !== '/' && normalizedCurrent.startsWith(normalizedTo + '/'))
    } catch (e) {
      console.error('Fehler beim Überprüfen des aktiven Links:', e)
      return false
    }
  }, [currentPath])

  return (
    <div 
      className={cn(
        "h-full border-r-2 border-black flex flex-col transition-all duration-300 ease-in-out bg-[#ff7a05]",
        isOpen ? "w-64" : "w-20"
      )} 
    >
      {/* Header */}
      <div className="p-4 flex items-center justify-between">
        {isOpen && (
          <div>
            <h2 className="text-xl font-bold tracking-tight">SFM Dashboard</h2>
            <p className="text-xs text-muted-foreground">{t("shopfloorManagement")}</p>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="h-8 w-8 p-0 ml-auto"
        >
          {isOpen ? (
            <PanelLeftClose className="h-5 w-5" />
          ) : (
            <PanelLeft className="h-5 w-5" />
          )}
          <span className="sr-only">Sidebar {isOpen ? 'schließen' : 'öffnen'}</span>
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-2 overflow-y-auto">
        <ul className="space-y-1">
          {navItems.map((item) => {
            return (
              <li key={item.to}>
                <Link
                  to={item.to}
                  className={cn(
                    "flex items-center gap-2 p-2 rounded-md transition-colors",
                    isLinkActive(item.to, item.exact)
                      ? 'bg-white text-black font-medium' 
                      : 'text-black hover:bg-white/20',
                    !isOpen && "justify-center"
                  )}
                  title={!isOpen ? item.name : undefined}
                  aria-label={item.name}
                >
                  <span>{item.icon}</span>
                  {isOpen && <span>{item.name}</span>}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
      
      {/* Einstellungen (vor dem Footer) */}
      <div className="p-2">
        <Link
          to={settingsItem.to}
          className={cn(
            "flex items-center gap-2 p-2 rounded-md transition-colors",
            isLinkActive(settingsItem.to)
              ? 'bg-white text-black font-medium' 
              : 'text-black hover:bg-white/20',
            !isOpen && "justify-center"
          )}
          title={!isOpen ? settingsItem.name : undefined}
          aria-label={settingsItem.name}
        >
          <span>{settingsItem.icon}</span>
          {isOpen && <span>{settingsItem.name}</span>}
        </Link>
      </div>

      {/* Footer */}
      <div className="p-4 border-t-2 border-black text-xs text-muted-foreground">
        <p>Shopfloor Management</p>
        <p>v1.0.0</p>
      </div>
    </div>
  )
}
