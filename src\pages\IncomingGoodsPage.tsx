import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { IncomingPositionsChart } from "@/components/charts/IncomingPositionsChart";
import { ReturnsChart } from "@/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";

/**
 * Incoming-Goods-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Incoming-Goods-Bereich an:
 * - Eingehende Positionen (WE-Daten) als Balkendiagramm
 * - Retouren als Kreisdiagramm
 */
export default function IncomingGoodsPage() {
  const { t } = useTranslation();

  // Zustand für den ausgewählten Datumsbereich - Standard: das ganze Jahr 2025
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 0, 1), // 1. Januar 2025
    to: new Date(2025, 0, 16), // 16. Januar 2025 (passend zu den verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black">WARENEINGANG</h1>
        <DateRangePicker 
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          {/* Eingehende Positionen */}
          <div className="xl:col-span-2">
            <IncomingPositionsChart dateRange={dateRange} />
          </div>

          {/* Retouren */}
          <div className="xl:col-span-1">
            <ReturnsChart dateRange={dateRange} />
          </div>
        </div>
      </div>
    </div>
  );
}
