import React, { useEffect, useState } from "react";

// Definition des Datentyps für die Ablägerei-Daten
interface AblaengereiDataPoint {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
}

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import { DateRange } from "react-day-picker";
import { isWithinInterval, parseISO } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

/**
 * CuttingsChart Komponente
 * 
 * Zeigt die Schneidemaschinen-Kennzahlen als Balkendiagramm an.
 * Verwendet Daten aus der Ablaengerei-Tabelle (cutTT, cutTR, cutRR)
 * 
 * Die X-Achse zeigt das Datum, die Y-Achse die Anzahl der Schnitte
 */

// Definition des Datentyps für die Schnitt-Daten im Chart
interface CuttingDataPoint {
  name: string;
  date?: Date; // Hilfsdatum für die Sortierung
  cutTT: number;
  cutTR: number;
  cutRR: number; // Korrigiert von cuteRR zu cutRR, um mit der Datenbank übereinzustimmen
  pickCut: number; // Neue Spalte für PickCut
}

// Definition des Datentyps für Datenbankzeilen
// Beachte: Die Feldnamen müssen exakt mit den Spaltennamen in der Datenbank übereinstimmen
interface DatabaseRow {
  id?: number;
  Datum?: string;
  cutTT?: number;
  cutTR?: number;
  cutRR?: number; // Neuer korrekter Spaltenname
  cuteRR?: number; // Alter Spaltenname (für Abwärtskompatibilität)
  cutGesamt?: number;
  [key: string]: any; // Erlaubt zusätzliche Eigenschaften
}

interface CuttingsChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: CuttingDataPoint[];
  // Datumsbereich für die Filterung der Daten
  dateRange?: DateRange;
}

/**
 * Filtert die Daten nach dem angegebenen Datumsbereich
 * 
 * @param data Die zu filternden Daten
 * @param dateRange Der Datumsbereich für die Filterung
 * @returns Die gefilterten Daten
 */
const filterDataByDateRange = (data: CuttingDataPoint[], dateRange: DateRange): CuttingDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;
  
  return data.filter(item => {
    // Wenn das Item kein Datum hat, können wir es nicht filtern
    if (!item.date) return true;
    
    // Überprüfe, ob das Datum im angegebenen Bereich liegt
    try {
      return isWithinInterval(item.date, {
        start: dateRange.from as Date, // Explizite Typumwandlung
        end: dateRange.to as Date // Explizite Typumwandlung
      });
    } catch (error) {
      console.error('Fehler beim Filtern nach Datum:', error);
      return true; // Im Fehlerfall das Item beibehalten
    }
  });
};

export function CuttingsChart({ data: propData, dateRange }: CuttingsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<CuttingDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    cutTT: {
      label: t("Trommel-Trommel"),
      color: "#00C6CF", // Kräftige Farbe im Neobrutalism-Stil
    },
    cutTR: {
      label: t("Trommel-Ring"),
      color: "#FF5470",
    },
    cutRR: {
      label: t("Ring-Ring"),
      color: "#FFB800",
    },
    pickCut: {
      label: t("PickCut"),
      color: "#ff8042",
    }
  };

  // Farben für die verschiedenen Balken im Neobrutalism-Stil
  const colors = ["#00C6CF", "#FF5470", "#FFB800", "#ff8042"];

  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
      const filteredData = dateRange ? filterDataByDateRange(propData, dateRange) : propData;
      setChartData(filteredData);
    } else {
      // Lade Daten aus der Datenbank
      loadData();
    }
  }, [propData, dateRange]);

  /**
   * Lädt die Daten aus der Ablaengerei-Tabelle der Datenbank
   * und filtert sie nach dem angegebenen Datumsbereich
   */
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Überprüfe, ob die Electron-API verfügbar ist
      if (!window.electronAPI) {
        console.error('Electron API ist nicht verfügbar!');
        setError('Electron API ist nicht verfügbar');
        return;
      }
      
      if (!window.electronAPI.database) {
        console.error('Datenbank-API ist nicht verfügbar!');
        setError('Datenbank-API ist nicht verfügbar');
        return;
      }
      
      if (!window.electronAPI.database.getAblaengereiData) {
        console.error('getAblaengereiData-Methode ist nicht verfügbar!');
        setError('getAblaengereiData-Methode ist nicht verfügbar');
        return;
      }
      
      console.log('Rufe getAblaengereiData auf...');
      // Versuche Daten aus der Datenbank zu laden
      const result = await window.electronAPI.database.getAblaengereiData();
      console.log('Ergebnis erhalten:', result);
      
      if (result && Array.isArray(result)) {
        console.log('Ablaengerei-Daten aus Datenbank erhalten:', result);
        
        if (result.length === 0) {
          console.warn('Keine Einträge in der Ablaengerei-Tabelle gefunden');
          setChartData([]);
          setError('Keine Daten in der Ablaengerei-Tabelle gefunden');
          return;
        }
        
        // Zeige den ersten Datensatz zur Inspektion der Struktur
        console.log('Erster Datensatz:', result[0]);
        console.log('Verfügbare Felder:', Object.keys(result[0]));
        
        // Debugging-Ausgaben für jeden Datensatz
        result.forEach((row: AblaengereiDataPoint, index: number) => {
          if (index < 3) { // Zeige nur die ersten 3 Datensätze, um die Konsole nicht zu überfüllen
            console.log(`Datensatz ${index}:`, row);
            console.log(`Datum: ${row.datum}, cutTT: ${row.cutTT}, cutTR: ${row.cutTR}, cutRR: ${row.cutRR}, pickCut: ${row.pickCut}`);
            console.log('Alle Felder in diesem Datensatz:', Object.keys(row).join(', '));
          }
        });
        
        // Konvertiere Ablaengerei-Daten zu Chart-Format
        console.log('Beginne mit der Konvertierung der Ablaengerei-Daten zum Chart-Format...');
        let processedData: CuttingDataPoint[] = result.map((row: AblaengereiDataPoint) => {
          // Formatiere das Datum für die X-Achse
          let formattedDate = 'Unbekannt';
          let dateObj = new Date(0); // Standard-Datum, falls keins vorhanden ist
          
          if (row.datum) {
            try {
              dateObj = new Date(row.datum);
              formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
              console.log(`Datum konvertiert: ${row.datum} -> ${formattedDate}`);
            } catch (e) {
              console.warn(`Fehler beim Formatieren des Datums ${row.datum}:`, e);
            }
          } else {
            console.warn('Datensatz ohne Datum gefunden!');
          }
          
          // Erstelle das Datenpunkt-Objekt mit Standardwerten für fehlende Felder
          const dataPoint: CuttingDataPoint = {
            name: formattedDate,
            date: dateObj, // Verwende das erstellte Date-Objekt
            cutTT: row.cutTT ?? 0, // Nullish coalescing für null/undefined
            cutTR: row.cutTR ?? 0,
            cutRR: row.cutRR ?? 0,
            pickCut: row.pickCut ?? 0 // Füge pickCut hinzu
          };
          
          console.log(`Datenpunkt erstellt: ${formattedDate}, TT=${dataPoint.cutTT}, TR=${dataPoint.cutTR}, RR=${dataPoint.cutRR}, pickCut=${dataPoint.pickCut}`);
          return dataPoint;
        });
        
        // Sortiere die Daten nach Datum aufsteigend
        console.log('Sortiere Daten nach Datum...');
        processedData.sort((a, b) => {
          const dateA = a.date ? a.date.getTime() : 0;
          const dateB = b.date ? b.date.getTime() : 0;
          return dateA - dateB;
        });
        
        // Zeige die sortierten Daten an
        console.log('Sortierte Daten:');
        processedData.forEach((item, index) => {
          if (index < 5) { // Zeige nur die ersten 5 Einträge
            console.log(`${index}: ${item.name}, TT=${item.cutTT}, TR=${item.cutTR}, RR=${item.cutRR}`);
          }
        });
        
        // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
        if (dateRange && dateRange.from && dateRange.to) {
          console.log(`Filtere Daten nach Datumsbereich: ${dateRange.from.toLocaleDateString()} bis ${dateRange.to.toLocaleDateString()}`);
          processedData = processedData.filter(item => {
            if (!item.date) return true;
            
            try {
              return isWithinInterval(item.date, {
                start: dateRange.from as Date,
                end: dateRange.to as Date
              });
            } catch (error) {
              console.error('Fehler beim Filtern nach Datum:', error);
              return true;
            }
          });
          console.log(`Nach Filterung verbleiben ${processedData.length} Datensätze`);
        }
        
        // Entferne das Hilfsdatum aus den Daten
        processedData = processedData.map(({ date, ...rest }) => rest) as CuttingDataPoint[];
        
        console.log('Finale Daten für das Chart:', processedData);
        setChartData(processedData);
      } else {
        console.error('Keine gültigen Daten erhalten, result =', result);
        throw new Error('Keine gültigen Daten erhalten');
      }
    } catch (err) {
      console.error('Fehler beim Laden der Ablaengerei-Daten:', err);
      setChartData([]);
      setError('Fehler beim Laden der Daten aus der Datenbank: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalTT = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutTT || 0), 0) : 0;
  const totalTR = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutTR || 0), 0) : 0;
  const totalRR = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.cutRR || 0), 0) : 0;
  const totalAll = totalTT + totalTR + totalRR;
  const avgTT = chartData.length > 0 ? totalTT / chartData.length : 0;
  const avgTR = chartData.length > 0 ? totalTR / chartData.length : 0;
  const avgRR = chartData.length > 0 ? totalRR / chartData.length : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">{t("check_database_connection")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black">
      <CardHeader>
        <CardTitle>SCHNITTE-KENNZAHLEN</CardTitle>
        <CardDescription>
          Übersicht der Schnittarten
        </CardDescription>
      </CardHeader>
      <CardContent>
          <ChartContainer
            config={chartConfig}
            className="h-60 w-full"
          >
            <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="name" 
                className="text-xs font-bold"
                tickLine={false}
                axisLine={false}
                tickMargin={1}
                height={30} // Höhe für zweizeilige Darstellung
                tick={({x, y, payload}: {x: number, y: number, payload: any}) => {
                  try {
                    // Sicherstellen, dass ein gültiger Wert vorhanden ist
                    if (!payload?.value) {
                      return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                    }
                    
                    // Extrahiere das Datum aus dem payload.value (Format: "DD.MM.YYYY")
                    const [day, month] = String(payload.value).split('.');
                    if (!day || !month) {
                      return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                    }
                    
                    const date = new Date();
                    date.setMonth(parseInt(month, 10) - 1);
                    date.setDate(parseInt(day, 10));
                    
                    // Wochentage auf Deutsch
                    const weekdays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
                    const weekday = weekdays[date.getDay()] || '-';
                    
                    return (
                      <g transform={`translate(${x},${y})`}>
                        <text x={0} y={0} dy={16} textAnchor="middle" fill="#000" className="text-[10px] sm:text-xs">
                          {`${day}.${month}`}
                        </text>
                        <text x={0} y={15} dy={16} textAnchor="middle" fill="#666" className="text-[9px] sm:text-[10px]">
                          {weekday}
                        </text>
                      </g>
                    );
                  } catch (e) {
                    console.error('Fehler beim Rendern des X-Achsen-Labels:', e);
                    return <text x={0} y={0} textAnchor="middle" fill="#000">-</text>;
                  }
                }}
              />
              <YAxis 
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                // Beschriftung für die Y-Achse
                label={{ 
                  value: "Anzahl Schnitte", 
                  angle: -90, 
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                  offset: -5
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelClassName="font-bold"
                    labelFormatter={(label) => `Datum: ${label}`}
                  />
                }
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Bar
                dataKey="cutTT"
                name="Trommel-Trommel"
                fill={colors[0]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
              <Bar
                dataKey="cutTR"
                name="Trommel-Ring"
                fill={colors[1]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
              <Bar
                dataKey="cutRR"
                name="Ring-Ring"
                fill={colors[2]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
              <Bar
                dataKey="pickCut"
                name="PickCut"
                fill="#ff8042"
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
            </BarChart>
          </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: TT: {chartData.length > 0 ? avgTT.toFixed(1) : 'N/A'} | 
              TR: {chartData.length > 0 ? avgTR.toFixed(1) : 'N/A'} | 
              RR: {chartData.length > 0 ? avgRR.toFixed(1) : 'N/A'}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt: {chartData.length > 0 ? totalAll : 0} | 
              {chartData.length > 0 
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank` 
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
