import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import { SystemAtrlChart } from "@/components/charts/SystemAtrlChart";
import { SystemArilChart } from "@/components/charts/SystemArilChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";

export default function SystemPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2014, 4, 1), // 1. Januar 2025
    to: new Date(2014, 4, 30), // 31. Dezember 2025
  });

  return (
     <div className="p-6 md:p-6">
       {/* Header mit Überschrift und Date Range Picker */}
       <div className="mb-2 flex justify-between items-center">
         <h1 className="text-3xl font-bold text-black">SYSTEM</h1>
         <DateRangePicker 
           value={dateRange}
           onChange={setDateRange}
           label="Da<PERSON><PERSON><PERSON><PERSON>hl"
         />
       </div>
       
       {/* Main Metrics */}
       <div className="mb-6">
         <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
           <div className="xl:col-span-1">
             <SystemAtrlChart dateRange={dateRange} />
           </div>
 
           <div className="xl:col-span-1">
             <SystemArilChart dateRange={dateRange} />
           </div>
         </div>
       </div>
    </div>
   );
 }
