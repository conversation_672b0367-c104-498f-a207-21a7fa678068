import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import { SystemAtrlChart } from "@/components/charts/SystemAtrlChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";

export default function SystemPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: addDays(new Date(new Date().getFullYear(), 0, 1), 10),
  });

  return (
    <div className="p-6 md:p-6">
      <div className="mb-2 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black">SYSTEM</h1>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          <SystemAtrlChart dateRange={dateRange} />
        </div>
      </div>
    </div>
  );
}
