import { createRoute } from "@tanstack/react-router";
import { rootRoute } from "./router";
import HomePage from "../pages/HomePage";
import DispatchPage from "@/pages/DispatchPage";
import CuttingPage from "@/pages/CuttingPage";
import IncomingGoodsPage from "@/pages/IncomingGoodsPage";
import SettingsPage from "@/pages/SettingsPage";
import SystemPage from "@/pages/SystemPage";
// AblaengereiTestPage wurde entfernt

/**
 * Routen-Konfiguration für die Shopfloor-Management-App
 * 
 * Definiert die verschiedenen Routen für:
 * - Home (Startseite)
 * - Dispatch (Versand-Bereich)
 * - Cutting (Schnitt-Bereich)
 * - Incoming Goods (Wareneingang-Bereich)
 * - Settings (Einstellungen)
 */

// Home-Route (Startseite)
export const HomeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: HomePage,
});

// Dispatch-Bereich Route
export const DispatchRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/dispatch",
  component: DispatchPage,
});

// Cutting-Bereich Route
export const CuttingRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/cutting",
  component: CuttingPage,
});

// Incoming-Goods-Bereich Route
export const IncomingGoodsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/incoming-goods",
  component: IncomingGoodsPage,
});

// Settings-Route
export const SettingsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/settings",
  component: SettingsPage,
});

// System-Route
export const SystemRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/system",
  component: SystemPage,
});

// Ablaengerei-Test-Route wurde entfernt

// Exportiere alle Routen für den Router
export const allRoutes = [
  HomeRoute, 
  DispatchRoute, 
  CuttingRoute, 
  IncomingGoodsRoute,
  SettingsRoute,
  SystemRoute,
];
