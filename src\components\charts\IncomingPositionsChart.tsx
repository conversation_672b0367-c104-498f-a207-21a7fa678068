import React, { useEffect, useState } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { DateRange } from "react-day-picker";
import { isWithinInterval, parseISO } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Definition des Datentyps für die WE-Daten aus der Datenbank
interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;
  weManl: number | null;
}

// Definition des Datentyps für die Chart-Daten
interface IncomingDataPoint {
  name: string;
  date?: Date; // Hilfsdatum für die Sortierung
  weAtrl: number;
  weManl: number;
}

/**
 * IncomingPositionsChart Komponente
 * 
 * Zeigt die eingehenden Positionen (WE-Daten) als Balkendiagramm an.
 * Verwendet Daten aus der dispatch_data-Tabelle (atrl, aril)
 * 
 * @param dateRange Datumsbereich für die Filterung der Daten
 */
interface IncomingPositionsChartProps {
  // Datumsbereich für die Filterung der Daten
  dateRange?: DateRange;
}

/**
 * Filtert die Daten nach dem angegebenen Datumsbereich
 * 
 * @param data Die zu filternden Daten
 * @param dateRange Der Datumsbereich für die Filterung
 * @returns Die gefilterten Daten
 */
const filterDataByDateRange = (data: IncomingDataPoint[], dateRange: DateRange): IncomingDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;
  
  return data.filter(item => {
    // Wenn das Item kein Datum hat, können wir es nicht filtern
    if (!item.date) return true;
    
    // Überprüfe, ob das Datum im angegebenen Bereich liegt
    try {
      return isWithinInterval(item.date, {
        start: dateRange.from as Date,
        end: dateRange.to as Date
      });
    } catch (error) {
      console.error('Fehler beim Filtern nach Datum:', error);
      return true; // Im Fehlerfall das Item beibehalten
    }
  });
};

export function IncomingPositionsChart({ dateRange }: IncomingPositionsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<IncomingDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    weAtrl: {
      label: t("WE Automatisch"),
      color: "#00C6CF", // Kräftige Farbe im Neobrutalism-Stil
    },
    weManl: {
      label: t("WE Manuell"),
      color: "#FF5470",
    },
  };

  // Farben für die verschiedenen Balken im Neobrutalism-Stil
  const colors = ["#00C6CF", "#FF5470"];

  // Lade Daten aus der Datenbank
  useEffect(() => {
    loadData();
  }, [dateRange]);

  /**
   * Lädt die WE-Daten aus der dispatch_data-Tabelle der Datenbank
   * und filtert sie nach dem angegebenen Datumsbereich
   */
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Überprüfe, ob die Electron-API verfügbar ist
      if (!window.electronAPI) {
        console.error('Electron API ist nicht verfügbar!');
        setError('Electron API ist nicht verfügbar');
        return;
      }
      
      if (!window.electronAPI.database) {
        console.error('Datenbank-API ist nicht verfügbar!');
        setError('Datenbank-API ist nicht verfügbar');
        return;
      }
      
      if (!window.electronAPI.database.getWEData) {
        console.error('getWEData-Methode ist nicht verfügbar!');
        setError('getWEData-Methode ist nicht verfügbar');
        return;
      }
      
      console.log('Rufe getWEData auf...');
      // Versuche WE-Daten aus der Datenbank zu laden
      const result = await window.electronAPI.database.getWEData();
      console.log('WE-Daten erhalten:', result);
      
      if (result && Array.isArray(result)) {
        console.log('WE-Daten aus Datenbank erhalten:', result);
        
        if (result.length === 0) {
          console.warn('Keine Einträge in der WE-Daten gefunden');
          setChartData([]);
          setError('Keine WE-Daten gefunden');
          return;
        }
        
        // Zeige den ersten Datensatz zur Inspektion der Struktur
        console.log('Erster WE-Datensatz:', result[0]);
        console.log('Verfügbare Felder:', Object.keys(result[0]));
        
        // Konvertiere WE-Daten zu Chart-Format
        console.log('Beginne mit der Konvertierung der WE-Daten zum Chart-Format...');
        let processedData: IncomingDataPoint[] = result.map((row: WEDataPoint) => {
          // Formatiere das Datum für die X-Achse
          let formattedDate = 'Unbekannt';
          let dateObj = new Date(0); // Standard-Datum, falls keins vorhanden ist
          
          if (row.datum) {
            try {
              dateObj = new Date(row.datum);
              formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
              console.log(`Datum konvertiert: ${row.datum} -> ${formattedDate}`);
            } catch (e) {
              console.warn(`Fehler beim Formatieren des Datums ${row.datum}:`, e);
            }
          } else {
            console.warn('Datensatz ohne Datum gefunden!');
          }
          
          // Erstelle das Datenpunkt-Objekt mit Standardwerten für fehlende Felder
          const dataPoint: IncomingDataPoint = {
            name: formattedDate,
            date: dateObj, // Verwende das erstellte Date-Objekt
            weAtrl: row.weAtrl ?? 0, // Nullish coalescing für null/undefined
            weManl: row.weManl ?? 0,
          };
          
          console.log(`WE-Datenpunkt erstellt: ${formattedDate}, weAtrl=${dataPoint.weAtrl}, weManl=${dataPoint.weManl}`);
          return dataPoint;
        });
        
        // Sortiere die Daten nach Datum aufsteigend
        console.log('Sortiere WE-Daten nach Datum...');
        processedData.sort((a, b) => {
          const dateA = a.date ? a.date.getTime() : 0;
          const dateB = b.date ? b.date.getTime() : 0;
          return dateA - dateB;
        });
        
        // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
        if (dateRange && dateRange.from && dateRange.to) {
          console.log(`Filtere WE-Daten nach Datumsbereich: ${dateRange.from.toLocaleDateString()} bis ${dateRange.to.toLocaleDateString()}`);
          processedData = processedData.filter(item => {
            if (!item.date) return true;
            
            try {
              return isWithinInterval(item.date, {
                start: dateRange.from as Date,
                end: dateRange.to as Date
              });
            } catch (error) {
              console.error('Fehler beim Filtern nach Datum:', error);
              return true;
            }
          });
          console.log(`Nach Filterung verbleiben ${processedData.length} WE-Datensätze`);
        }
        
        // Entferne das Hilfsdatum aus den Daten
        processedData = processedData.map(({ date, ...rest }) => rest) as IncomingDataPoint[];
        
        console.log('Finale WE-Daten für das Chart:', processedData);
        setChartData(processedData);
      } else {
        console.error('Keine gültigen WE-Daten erhalten, result =', result);
        throw new Error('Keine gültigen WE-Daten erhalten');
      }
    } catch (err) {
      console.error('Fehler beim Laden der WE-Daten:', err);
      setChartData([]);
      setError('Fehler beim Laden der WE-Daten aus der Datenbank: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalAtrl = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.weAtrl || 0), 0) : 0;
  const totalManl = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.weManl || 0), 0) : 0;
  const totalAll = totalAtrl + totalManl;
  const avgAtrl = chartData.length > 0 ? totalAtrl / chartData.length : 0;
  const avgManl = chartData.length > 0 ? totalManl / chartData.length : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">{t("check_database_connection")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black">
      <CardHeader>
        <CardTitle>WARENEINGANG POSITIONEN</CardTitle>
        <CardDescription>
          Wareningänge ins ATrL und MAnL
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-60 w-full"
        >
          <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="name" 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
            />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              // Beschriftung für die Y-Achse
              label={{ 
                value: "Anzahl Positionen", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="weAtrl"
              name="WE Automatisch"
              fill={colors[0]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="weManl"
              name="WE Manuell"
              fill={colors[1]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: Automatisch: {chartData.length > 0 ? avgAtrl.toFixed(1) : 'N/A'} | 
              Manuell: {chartData.length > 0 ? avgManl.toFixed(1) : 'N/A'}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt: {chartData.length > 0 ? totalAll : 0} | 
              {chartData.length > 0 
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank` 
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
