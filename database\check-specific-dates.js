// Skript zum Überprüfen spezifischer Datensätze aus dem Screenshot
const Database = require('better-sqlite3');
const path = require('path');

// Pfad zur Datenbank
const dbPath = path.join(__dirname, 'sfm_dashboard.db');

try {
  // Verbinde zur Datenbank
  const db = new Database(dbPath);
  
  console.log('Überprüfe spezifische Datensätze aus dem Screenshot...\n');
  
  // Suche nach dem spezifischen Datum aus dem Screenshot (2020-07-27)
  const stmt = db.prepare('SELECT * FROM Ablaengerei WHERE Datum = ?');
  const result = stmt.get('2020-07-27');
  console.log('Datensatz für 2020-07-27:');
  if (result) {
    console.log(`ID: ${result.id}`);
    console.log(`Datum: ${result.Datum}`);
    console.log(`lagerCut200: ${result.lagerCut200}`);
    console.log(`lagerCut220: ${result.lagerCut220}`);
    console.log(`lagerCut240: ${result.lagerCut240}`);
    console.log(`cutLagerK200: ${result.cutLagerK200}`);
    console.log(`cutLagerK220: ${result.cutLagerK220}`);
    console.log(`cutLagerK240: ${result.cutLagerK240}`);
  } else {
    console.log('Nicht gefunden!');
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Suche nach Datensätzen aus 2020/2021 (die im Screenshot sichtbar sind)
  const stmt2 = db.prepare(`
    SELECT * FROM Ablaengerei 
    WHERE (Datum LIKE '2020%' OR Datum LIKE '2021%') 
    ORDER BY Datum DESC 
    LIMIT 10
  `);
  const results2 = stmt2.all();
  console.log('Datensätze aus 2020/2021:');
  console.log(`Gefunden: ${results2.length} Datensätze\n`);
  
  results2.forEach((row, index) => {
    console.log(`${index + 1}. Datum: ${row.Datum}`);
    console.log(`   lagerCut200: ${row.lagerCut200}`);
    console.log(`   lagerCut220: ${row.lagerCut220}`);
    console.log(`   lagerCut240: ${row.lagerCut240}`);
    console.log(`   cutLagerK200: ${row.cutLagerK200}`);
    console.log(`   cutLagerK220: ${row.cutLagerK220}`);
    console.log(`   cutLagerK240: ${row.cutLagerK240}`);
    console.log('');
  });
  
  // Prüfe, ob es überhaupt Datensätze mit nicht-null Lagerwerten gibt
  console.log('='.repeat(50));
  const stmt3 = db.prepare(`
    SELECT COUNT(*) as count FROM Ablaengerei 
    WHERE lagerCut200 IS NOT NULL 
       OR lagerCut220 IS NOT NULL 
       OR lagerCut240 IS NOT NULL
       OR cutLagerK200 IS NOT NULL
       OR cutLagerK220 IS NOT NULL
       OR cutLagerK240 IS NOT NULL
  `);
  const countResult = stmt3.get();
  console.log(`\nAnzahl Datensätze mit nicht-null Lagerwerten: ${countResult.count}`);
  
  // Schließe die Datenbankverbindung
  db.close();
  
  console.log('\nÜberprüfung abgeschlossen.');
  
} catch (error) {
  console.error('Fehler beim Überprüfen der Daten:', error);
} 