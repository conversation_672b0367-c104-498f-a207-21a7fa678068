import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import { CuttingsChart } from "@/components/charts/CuttingsChart";
import { ReturnsChart } from "@/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { LagerCutsChart } from "@/components/charts/LagerCutsChart";

/**
 * Cutting-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Cutting-Bereich an:
 * - Cuttings nach Maschinen als Balkendiagramm
 * - Retouren als Kreisdiagramm
 * - Lagerbestand Schnitte als Balkendiagramm
 */
export default function CuttingPage() {
  const { t } = useTranslation();

  // Zustand für den ausgewählten Datumsbereich - Standard: das ganze Jahr 2025
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 4, 1), // 1. Januar 2025
    to: new Date(2025, 4, 15), // 31. Dezember 2025
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-black">ABLÄNGEREI</h1>
        <DateRangePicker 
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            <LagerCutsChart dateRange={dateRange} />
          </div>

          <div className="xl:col-span-1">
            <ReturnsChart dateRange={dateRange} />
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="xl:col-span-1">
            <CuttingsChart dateRange={dateRange} />
          </div>
        </div>
      </div>
    </div>
  );
}
