import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SystemArilDataPoint } from "@/services/database";

interface SystemArilChartProps {
  dateRange?: DateRange;
}

export function SystemArilChart({ dateRange }: SystemArilChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<SystemArilDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const chartConfig = {
    ninioSapAril: { label: t("SAP"), color: "#8884d8" },
    nioWitronAril: { label: t("Witron"), color: "#82ca9d" },
    nioSiemensAril: { label: t("Siemens"), color: "#ffc658" },
    nioProzessAril: { label: t("Prozess"), color: "#ff8042" },
    nioSonstigesAril: { label: t("Sonstiges"), color: "#0088FE" },
  };

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("SystemArilChart: Starte Datenladung...");

      if (!window.electronAPI?.database?.getSystemArilData) {
        console.error("SystemArilChart: Datenbank-API nicht verfügbar");
        setError("Datenbank-API nicht verfügbar");
        return;
      }

      console.log("SystemArilChart: Rufe getSystemArilmData auf...");
      const result = await window.electronAPI.database.getSystemArilData();
      console.log("SystemArilChart: Erhaltene Daten:", result);

      if (result && Array.isArray(result)) {
        console.log(`SystemArilChart: ${result.length} Datensätze erhalten`);

        // Filtere Daten basierend auf dem ausgewählten Datumsbereich
        let filteredData = result;
        if (dateRange?.from || dateRange?.to) {
          console.log("SystemArilChart: Filtere Daten nach Datumsbereich:", dateRange);
          filteredData = result.filter((item) => {
            try {
              // Konvertiere das Datum aus dem deutschen Format zurück zu einem Date-Objekt
              const dateParts = item.Datum.split('.');
              let itemDate: Date;

              if (dateParts.length === 3) {
                // Format: DD.MM.YYYY
                itemDate = new Date(parseInt(dateParts[2]), parseInt(dateParts[1]) - 1, parseInt(dateParts[0]));
              } else {
                // Fallback: versuche direktes Parsing
                itemDate = new Date(item.Datum);
              }

              if (dateRange?.from && itemDate < dateRange.from) return false;
              if (dateRange?.to && itemDate > dateRange.to) return false;

              return true;
            } catch (e) {
              console.error('SystemArilChart: Fehler bei der Datumsfilterung:', e, 'für Datum:', item.Datum);
              return true; // Bei Fehlern beim Datumsvergleich den Datenpunkt behalten
            }
          });
          console.log(`SystemArilChart: Nach Filterung ${filteredData.length} Datensätze übrig`);
        }

        setChartData(filteredData);
      } else {
        console.error("SystemArilChart: Keine gültigen Daten erhalten:", result);
        throw new Error("Keine gültigen Daten erhalten");
      }
    } catch (error) {
      console.error("SystemArilChart: Fehler beim Laden der Daten:", error);
      setError("Fehler beim Laden der Daten");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>{t("loading")}...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  // Berechne Durchschnittswerte für Footer
  const avgSap = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.ninioSapAril, 0) / chartData.length : 0;
  const avgWitron = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.nioWitronAril, 0) / chartData.length : 0;
  const avgSiemens = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.nioSiemensAril, 0) / chartData.length : 0;
  const totalAll = chartData.reduce((sum, item) => sum + item.ninioSapAril + item.nioWitronAril + item.nioSiemensAril + item.nioProzessAril + item.nioSonstigesAril, 0);

  return (
    <Card className="text-black">
      <CardHeader>
        <CardTitle>Aril NIO FAHRTEN</CardTitle>
        <CardDescription>
          System versursachte NIO Fahrten
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-60 w-full">
          <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="Datum"
              className="text-xs font-bold"
              tickLine={false}
              axisLine={false}
              tickMargin={1}
              height={30}
              tick={({x, y, payload}: {x: number, y: number, payload: any}) => {
                try {
                  if (!payload?.value) {
                    return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                  }

                  const [day, month] = String(payload.value).split('.');
                  if (!day || !month) {
                    return <text x={x} y={y} textAnchor="middle" fill="#000">-</text>;
                  }

                  const date = new Date();
                  date.setMonth(parseInt(month, 10) - 1);
                  date.setDate(parseInt(day, 10));

                  const weekdays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
                  const weekday = weekdays[date.getDay()] || '-';

                  return (
                    <g transform={`translate(${x},${y})`}>
                      <text x={0} y={0} dy={16} textAnchor="middle" fill="#000" className="text-[10px] sm:text-xs">
                        {`${day}.${month}`}
                      </text>
                      <text x={0} y={15} dy={16} textAnchor="middle" fill="#666" className="text-[9px] sm:text-[10px]">
                        {weekday}
                      </text>
                    </g>
                  );
                } catch (e) {
                  console.error('Fehler beim Rendern des X-Achsen-Labels:', e);
                  return <text x={0} y={0} textAnchor="middle" fill="#000">-</text>;
                }
              }}
            />
            <YAxis
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              label={{
                value: "Aril-Werte",
                angle: -90,
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="ninioSapAril"
              name={chartConfig.ninioSapAril.label}
              fill={chartConfig.ninioSapAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioWitronAril"
              name={chartConfig.nioWitronAril.label}
              fill={chartConfig.nioWitronAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioSiemensAril"
              name={chartConfig.nioSiemensAril.label}
              fill={chartConfig.nioSiemensAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioProzessAril"
              name={chartConfig.nioProzessAril.label}
              fill={chartConfig.nioProzessAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="nioSonstigesAril"
              name={chartConfig.nioSonstigesAril.label}
              fill={chartConfig.nioSonstigesAril.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: SAP: {chartData.length > 0 ? avgSap.toFixed(1) : 'N/A'} |
              Witron: {chartData.length > 0 ? avgWitron.toFixed(1) : 'N/A'} |
              Siemens: {chartData.length > 0 ? avgSiemens.toFixed(1) : 'N/A'}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt: {chartData.length > 0 ? totalAll : 0} |
              {chartData.length > 0
                ? `Basierend auf ${chartData.length} Einträgen aus der Datenbank`
                : 'Keine Daten verfügbar'}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
