import Database from 'better-sqlite3';
import fs from 'fs';

// Schnittstellen für die Datenbank
export interface AblaengereiRecord {
  id: number;
  datum: string;
  cutLagerK220: number;
  cutLagerR220: number;
  lagerCut220: number;
  cutLagerK240: number;
  cutLagerR240: number;
  lagerCut240: number;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  cutGesamt: number;
  pickCut: number;
  cutLager200: number;
  cutLagerK200: number;
  lagerCut200: number;
}

export interface CuttingDataPoint {
  name: string;
  date: Date;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  pickCut: number;
}

export interface LagerCutsDataPoint {
  name: string;
  date: Date;
  lagerSumme: number;
  cutLagerKSumme: number;
  cutLagerRSumme: number;
}

/**
 * WE-Datenpunkt (Wareneingang)
 */
export interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;  // Entspricht atrl in dispatch_data
  weManl: number | null;  // Entspricht aril in dispatch_data
}

export interface SystemAtrlDataPoint {
  Datum: string;
  ninioSapAtrl: number;
  nioWitronAtrl: number;
  nioSiemensAtrl: number;
  nioProzessAtrl: number;
  nioSonstigesAtrl: number;
}

class DatabaseService {
  private db: Database.Database | null = null;
  private dbPath = './database/sfm_dashboard.db';

  constructor() {
    const dbDir = './database';
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    this.initDatabase();
  }

  private initDatabase(): void {
    try {
      this.db = new Database(this.dbPath);
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS ablaengerei (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          datum DATE NOT NULL,
          cutLagerK220 INTEGER,
          cutLagerR220 INTEGER,
          lagerCut220 INTEGER,
          cutLagerK240 INTEGER,
          cutLagerR240 INTEGER,
          lagerCut240 INTEGER,
          cutTT INTEGER,
          cutTR INTEGER,
          cutRR INTEGER,
          cutGesamt INTEGER,
          pickCut INTEGER,
          cutLager200 INTEGER,
          cutLagerK200 INTEGER,
          lagerCut200 INTEGER
        );

        CREATE TABLE IF NOT EXISTS dispatch_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          datum DATE NOT NULL,
          tag INTEGER,
          monat INTEGER,
          kw INTEGER,
          jahr INTEGER,
          servicegrad REAL,
          ausgeliefert_lup INTEGER,
          rueckstaendig INTEGER,
          produzierte_tonnagen REAL,
          direktverladung_kiaa INTEGER,
          umschlag INTEGER,
          kg_pro_colli REAL,
          elefanten REAL,
          atrl INTEGER,
          aril INTEGER,
          fuellgrad_aril REAL,
          qm_angenommen INTEGER,
          qm_abgelehnt INTEGER,
          qm_offen INTEGER,
          mitarbeiter_std REAL
        );

        CREATE TABLE IF NOT EXISTS System (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          Datum DATE NOT NULL,
          ninioSapAtrl INTEGER DEFAULT 0,
          nioWitronAtrl INTEGER DEFAULT 0,
          nioSiemensAtrl INTEGER DEFAULT 0,
          nioProzessAtrl INTEGER DEFAULT 0,
          nioSonstigesAtrl INTEGER DEFAULT 0
        );
      `);
      
      console.log('Datenbank wurde erfolgreich initialisiert.');
    } catch (error) {
      console.error('Fehler beim Initialisieren der Datenbank:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Schließt die Datenbankverbindung
   */
  public closeDatabase(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  /**
   * Ruft die Servicelevel-Daten für das Diagramm ab
   * @returns Array von Servicelevel-Datenpunkten
   */
  public getServiceLevelData() {
    if (!this.db) {
      console.error('Keine Datenbankverbindung verfügbar');
      return [];
    }
    
    try {
      // Prüfe zuerst, ob die Tabelle existiert und Daten enthält
      const tableCheck = this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='dispatch_data'
      `).get();
      
      if (!tableCheck) {
        console.error('Tabelle dispatch_data existiert nicht');
        return [];
      }
      
      // Prüfe, ob Daten in der Tabelle vorhanden sind
      const countCheck = this.db.prepare(`
        SELECT COUNT(*) as count FROM dispatch_data
      `).get() as { count: number };
      
      console.log('Anzahl der Datensätze in dispatch_data:', countCheck.count);
      
      if (countCheck.count === 0) {
        console.error('Keine Daten in der Tabelle dispatch_data');
        // Importiere Daten aus der CSV-Datei
        const importResult = this.importCsvData();
        if (importResult) {
          console.log('CSV-Daten wurden importiert, versuche erneut Daten abzurufen');
          // Versuche erneut, die Daten abzurufen
          return this.getServiceLevelDataAfterImport();
        }
        return [];
      }
      
      // Wir verwenden jetzt direkt das Feld 'servicegrad' ohne Umbenennung
      const stmt = this.db.prepare(`
        SELECT datum, servicegrad
        FROM dispatch_data
        ORDER BY datum ASC
      `);
      
      const results = stmt.all();
      console.log('Servicegrad-Daten aus der Datenbank:', results);
      return results;
    } catch (error) {
      console.error('Fehler beim Abrufen der Servicegrad-Daten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }
  
  /**
   * Ruft die Servicelevel-Daten nach dem Import ab
   * @returns Array von Servicelevel-Datenpunkten
   */
  private getServiceLevelDataAfterImport() {
    if (!this.db) return [];
    
    try {
      const stmt = this.db.prepare(`
        SELECT datum, servicegrad
        FROM dispatch_data
        ORDER BY datum ASC
      `);
      
      const results = stmt.all();
      console.log('Servicegrad-Daten nach Import:', results);
      return results;
    } catch (error) {
      console.error('Fehler beim Abrufen der Servicegrad-Daten nach Import:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }
  
  /**
   * Importiert die CSV-Daten in die Datenbank
   * @returns true, wenn der Import erfolgreich war, sonst false
   */
  private importCsvData(): boolean {
    try {
      // Verwende einen absoluten Pfad zur CSV-Datei
      const path = require('path');
      const csvPath = path.resolve(__dirname, '../../database/imigration/zusammencsv.csv');
      console.log('Versuche CSV-Datei zu laden von:', csvPath);
      const fs = require('fs');
      
      if (!fs.existsSync(csvPath)) {
        console.error('CSV-Datei nicht gefunden:', csvPath);
        return false;
      }
      
      const csvContent = fs.readFileSync(csvPath, 'utf-8');
      const lines = csvContent.split('\n');
      
      if (lines.length < 2) {
        console.error('CSV-Datei enthält nicht genügend Daten');
        return false;
      }
      
      // Bereite die Einfügeanweisung vor
      // Prüfe, ob die Datenbankverbindung vorhanden ist
      if (!this.db) {
        console.error('Keine Datenbankverbindung verfügbar');
        return false;
      }
      
      const insert = this.db.prepare(`
        INSERT INTO dispatch_data 
        (datum, tag, monat, kw, jahr, servicegrad, ausgeliefert_lup, rueckstaendig, 
        produzierte_tonnagen, direktverladung_kiaa, umschlag, kg_pro_colli, elefanten, 
        atrl, aril, fuellgrad_aril, qm_angenommen, qm_abgelehnt, qm_offen, mitarbeiter_std)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      // Führe die Einfügungen in einer Transaktion durch
      const insertMany = this.db.transaction((data: any[]) => {
        for (const item of data) {
          insert.run(...item);
        }
      });
      
      // Verarbeite die Daten
      const data: any[] = [];
      
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // Teile die Zeile bei Semikolon
        const values = line.split(';');
        
        // Überprüfe, ob genügend Werte vorhanden sind
        if (values.length < 20) {
          console.warn(`Zeile ${i + 1} hat nicht genügend Werte und wird übersprungen`);
          continue;
        }
        
        // Extrahiere die Werte und konvertiere sie in die richtigen Typen
        const datum = values[0];
        const tag = this.getTagNumber(values[1]); // Konvertiere Wochentag in Zahl
        const monat = this.getMonthNumber(values[2]); // Extrahiere Monatszahl
        const jahr = parseInt(values[3]);
        const kw = parseInt(values[4].replace('KW ', ''));
        const servicegrad = parseFloat(values[5].replace(',', '.'));
        const ausgeliefert_lup = parseInt(values[6]);
        const rueckstaendig = parseInt(values[7]);
        const produzierte_tonnagen = parseFloat(values[8].replace(',', '.'));
        const direktverladung_kiaa = parseInt(values[9]);
        const umschlag = parseInt(values[10]);
        const kg_pro_colli = parseFloat(values[11].replace(',', '.'));
        const elefanten = parseFloat(values[12].replace(',', '.'));
        const atrl = parseInt(values[13]);
        const aril = parseInt(values[14]);
        const fuellgrad_aril = parseFloat(values[15].replace(',', '.'));
        const qm_angenommen = parseInt(values[16]);
        const qm_abgelehnt = parseInt(values[17]);
        const qm_offen = parseInt(values[18]);
        const mitarbeiter_std = parseInt(values[19]);
        
        // Füge die Daten zum Array hinzu
        data.push([
          datum, tag, monat, kw, jahr, servicegrad, ausgeliefert_lup, rueckstaendig,
          produzierte_tonnagen, direktverladung_kiaa, umschlag, kg_pro_colli, elefanten,
          atrl, aril, fuellgrad_aril, qm_angenommen, qm_abgelehnt, qm_offen, mitarbeiter_std
        ]);
      }
      
      // Führe die Einfügungen durch
      insertMany(data);
      
      console.log(`${data.length} Datensätze wurden erfolgreich importiert.`);
      return true;
    } catch (error) {
      console.error('Fehler beim Importieren der CSV-Datei:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }
  
  /**
   * Konvertiert einen Wochentagsnamen in eine Zahl (1 = Montag, 7 = Sonntag)
   */
  private getTagNumber(weekdayName: string): number {
    const weekdays: { [key: string]: number } = {
      'Montag': 1,
      'Dienstag': 2,
      'Mittwoch': 3,
      'Donnerstag': 4,
      'Freitag': 5,
      'Samstag': 6,
      'Sonntag': 7
    };
    
    return weekdays[weekdayName] || 0;
  }
  
  /**
   * Extrahiert die Monatsnummer aus einem String wie "Januar 2025"
   */
  private getMonthNumber(monthYearString: string): number {
    const months: { [key: string]: number } = {
      'Januar': 1,
      'Februar': 2,
      'März': 3,
      'April': 4,
      'Mai': 5,
      'Juni': 6,
      'Juli': 7,
      'August': 8,
      'September': 9,
      'Oktober': 10,
      'November': 11,
      'Dezember': 12
    };

    return months[monthYearString.split(' ')[0]] || 0;
  }

  /**
   * Ruft die Kommissionierungsdaten für das Diagramm ab
   * @returns Array von Kommissionierungsdatenpunkten
   */
  public getPickingData() {
    if (!this.db) return [];

    try {
      const stmt = this.db.prepare(`
        SELECT
          datum as date,
          atrl,
          aril,
          fuellgrad_aril
        FROM dispatch_data
        WHERE atrl IS NOT NULL AND aril IS NOT NULL AND fuellgrad_aril IS NOT NULL
        ORDER BY datum ASC
      `);

      return stmt.all();
    } catch (error) {
      console.error('Fehler beim Abrufen der Kommissionierungsdaten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die Schneidedaten für den CuttingsChart ab
   * @returns Array von Schnittdaten mit cutTT, cutTR, cutRR und pickCut
   */
  public getCuttingsData(): CuttingDataPoint[] {
    if (!this.db) return [];

    try {
      const stmt = this.db.prepare(`
        SELECT
          Datum as name,
          Datum as date,
          COALESCE(cutTT, 0) as cutTT,
          COALESCE(cutTR, 0) as cutTR,
          COALESCE(cutRR, 0) as cutRR,
          COALESCE(pickCut, 0) as pickCut
        FROM Ablaengerei
        WHERE cutTT IS NOT NULL
          AND cutTR IS NOT NULL
          AND cutRR IS NOT NULL
        ORDER BY Datum ASC
      `);

      return stmt.all().map((row: any) => ({
        ...row,
        date: new Date(row.date)
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Schneidedaten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die Lagerdaten für den LagerCutsChart ab
   * @returns Array von Lagerdaten
   */
  public getLagerCutsData(): LagerCutsDataPoint[] {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }

    try {
      console.log('Starte Datenbankabfrage für Lagerdaten...');
      
      // Prüfe zuerst, ob die Tabelle existiert
      const tableCheck = this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='Ablaengerei'
      `).get();
      console.log('Tabellen-Check Ergebnis:', tableCheck);
      
      if (!tableCheck) {
        console.error('Tabelle Ablaengerei existiert nicht');
        return [];
      }
      
      // Prüfe, ob Daten in der Tabelle vorhanden sind
      const countCheck = this.db.prepare(`
        SELECT COUNT(*) as count FROM Ablaengerei
      `).get() as { count: number };
      console.log('Anzahl der Datensätze in Ablaengerei:', countCheck.count);
      
      if (countCheck.count === 0) {
        console.error('Keine Daten in der Tabelle Ablaengerei');
        return [];
      }
      
      // Prüfe die Tabellenstruktur
      const columns = this.db.prepare(`PRAGMA table_info(Ablaengerei)`).all();
      console.log('Tabellen-Spalten:', columns.map((col: any) => col.name));
      
      // Zeige ein paar Beispieldatensätze
      const sampleData = this.db.prepare(`SELECT * FROM Ablaengerei LIMIT 3`).all();
      console.log('Beispieldaten aus Ablaengerei:', sampleData);
      
      // Da die Lagerdaten alle null sind, verwenden wir temporär die Schnittdaten
      // um zu zeigen, dass der Chart funktioniert
      const stmt = this.db.prepare(`
        SELECT
          Datum as name,
          Datum as date,
          COALESCE(cutTT, 0) as lagerSumme,
          COALESCE(cutTR, 0) as cutLagerKSumme,
          COALESCE(cutRR, 0) as cutLagerRSumme
        FROM Ablaengerei
        WHERE Datum IS NOT NULL
        ORDER BY Datum ASC
      `);

      const results = stmt.all().map((row: any) => ({
        ...row,
        date: new Date(row.date)
      }));
      
      console.log(`Lagerdaten abgerufen: ${results.length} Einträge gefunden`);
      if (results.length > 0) {
        console.log('Beispiel Lagerdaten (erster Eintrag):', results[0]);
        console.log('Beispiel Lagerdaten (alle ersten 3):', results.slice(0, 3));
      }
      
      return results;
    } catch (error) {
      console.error('Fehler beim Abrufen der Lagerdaten:', error instanceof Error ? error.message : String(error));
      console.error('Stack trace:', error instanceof Error ? error.stack : 'Kein Stack verfügbar');
      return [];
    }
  }

  /**
   * Ruft die Ablägerei-Daten für die Charts ab
   * @returns Array von Ablägerei-Datensätzen
   */
  public getAblaengereiData(): AblaengereiRecord[] {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }
    
    try {
      console.log('Starte Datenbankabfrage für Ablägerei-Daten...');
      
      const stmt = this.db.prepare(`
        SELECT 
          id,
          Datum as datum,
          cutLagerK220,
          cutLagerR220,
          lagerCut220,
          cutLagerK240,
          cutLagerR240,
          lagerCut240,
          cutTT,
          cutTR,
          cutRR,
          cutGesamt,
          pickCut,
          cutLager200,
          cutLagerK200,
          lagerCut200
        FROM Ablaengerei
        ORDER BY Datum ASC
      `);
      
      const results = stmt.all();
      console.log(`Ablägerei-Daten abgerufen: ${results.length} Einträge gefunden`);
      
      if (results.length > 0) {
        console.log('Beispiel Ablägerei-Daten (erster Eintrag):', results[0]);
        
        // Konvertiere die Daten
        const normalizedResults = results.map((row: any) => {
          const normalizedRow: any = {};
          Object.entries(row).forEach(([key, value]) => {
            normalizedRow[key.charAt(0).toLowerCase() + key.slice(1)] = value;
          });
          
          return normalizedRow as AblaengereiRecord;
        });

        console.log('Normalisierte Daten (erster Eintrag):', normalizedResults[0]);
        return normalizedResults;
      }
      
      return [];
    } catch (error) {
      console.error('Fehler beim Abrufen der Ablägerei-Daten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die Retourendaten für das Diagramm ab
   * @returns Array von Retourendatenpunkten
   */
  public getReturnsData() {
    if (!this.db) return [];
    
    try {
      const stmt = this.db.prepare(`
        SELECT 
          'QM Offen' as name, 
          SUM(qm_offen) as value,
          'var(--chart-1)' as fill
        FROM dispatch_data
        WHERE qm_offen IS NOT NULL
        UNION ALL
        SELECT 
          'QM Abgelehnt' as name, 
          SUM(qm_abgelehnt) as value,
          'var(--chart-2)' as fill
        FROM dispatch_data
        WHERE qm_abgelehnt IS NOT NULL
        UNION ALL
        SELECT 
          'QM Angenommen' as name, 
          SUM(qm_angenommen) as value,
          'var(--chart-3)' as fill
        FROM dispatch_data
        WHERE qm_angenommen IS NOT NULL
      `);
      
      return stmt.all();
    } catch (error) {
      console.error('Fehler beim Abrufen der Retourendaten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die Lieferpositionsdaten für das Diagramm ab
   * @returns Array von Lieferpositionsdaten mit ausgeliefert_lup und rueckstaendig
   */
  public getDeliveryPositionsData() {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }
    
    try {
      console.log('Starte Datenbankabfrage für Lieferpositionen...');
      const stmt = this.db.prepare(`
        SELECT 
          datum as date,
          ausgeliefert_lup,
          rueckstaendig
        FROM dispatch_data
        ORDER BY datum ASC
      `);
      
      const results = stmt.all();
      console.log(`Lieferpositionsdaten abgerufen: ${results.length} Einträge gefunden`);
      
      if (results.length > 0) {
        console.log('Beispieldaten (erster Eintrag):', results[0]);
      }
      
      return results;
    } catch (error) {
      console.error('Fehler beim Abrufen der Lieferpositionsdaten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft alle Ablägerei-Daten ab (für Abwärtskompatibilität)
   * @returns Array von Ablägerei-Datensätzen
   */
  public getAblaengereiDataAsync = async (): Promise<AblaengereiRecord[]> => {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }
    
    try {
      console.log('Starte Datenbankabfrage für Ablägerei-Daten...');
      
      const stmt = this.db.prepare(`
        SELECT 
          id,
          Datum as datum,
          cutLagerK220,
          cutLagerR220,
          lagerCut220,
          cutLagerK240,
          cutLagerR240,
          lagerCut240,
          cutTT,
          cutTR,
          cutRR,
          cutGesamt,
          pickCut,
          cutLager200,
          cutLagerK200,
          lagerCut200
        FROM Ablaengerei
        ORDER BY Datum ASC
      `);
      
      const results = stmt.all();
      console.log(`Ablägerei-Daten abgerufen: ${results.length} Einträge gefunden`);
      
      if (results.length > 0) {
        console.log('Beispiel Ablägerei-Daten (erster Eintrag):', results[0]);
        
        // Konvertiere die Daten
        const normalizedResults = results.map((row: any) => {
          const normalizedRow: any = {};
          Object.entries(row).forEach(([key, value]) => {
            normalizedRow[key.charAt(0).toLowerCase() + key.slice(1)] = value;
          });
          
          return normalizedRow as AblaengereiRecord;
        });

        console.log('Normalisierte Daten (erster Eintrag):', normalizedResults[0]);
        return normalizedResults;
      }
      
      return [];
    } catch (error) {
      console.error('Fehler beim Abrufen der Ablägerei-Daten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die Tagesleistungsdaten für das Diagramm ab
   * @returns Array von Tagesleistungsdaten mit produzierten Tonnagen, Direktverladung KIAA, Umschlag, kg pro Colli und Elefanten
   */
  public getTagesleistungData() {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }
    
    try {
      console.log('Starte Datenbankabfrage für Tagesleistungsdaten...');
      const stmt = this.db.prepare(`
        SELECT 
          datum as date,
          COALESCE(produzierte_tonnagen, 0) as produzierte_tonnagen,
          COALESCE(direktverladung_kiaa, 0) as direktverladung_kiaa,
          COALESCE(umschlag, 0) as umschlag,
          COALESCE(kg_pro_colli, 0) as kg_pro_colli,
          COALESCE(elefanten, 0) as elefanten
        FROM dispatch_data
        ORDER BY datum ASC
      `);
      
      const results = stmt.all();
      console.log(`Tagesleistungsdaten abgerufen: ${results.length} Einträge gefunden`);
      
      if (results.length > 0) {
        console.log('Beispieldaten (erster Eintrag):', results[0]);
      }
      
      return results;
    } catch (error) {
      console.error('Fehler beim Abrufen der Tagesleistungsdaten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
   * @returns Array von WE-Datenpunkten mit weAtrl und weManl
   */
  public getWEData(): WEDataPoint[] {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }
    
    try {
      console.log('Starte Datenbankabfrage für WE-Daten (Wareneingang)...');
      
      // Prüfe zuerst, ob die Tabelle existiert
      const tableCheck = this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='dispatch_data'
      `).get();
      
      if (!tableCheck) {
        console.error('Tabelle dispatch_data existiert nicht');
        return [];
      }
      
      // Lade die Daten aus der dispatch_data Tabelle
      const stmt = this.db.prepare(`
        SELECT 
          id,
          datum,
          COALESCE(atrl, 0) as weAtrl,
          COALESCE(aril, 0) as weManl
        FROM dispatch_data
        WHERE atrl IS NOT NULL 
          OR aril IS NOT NULL
        ORDER BY datum ASC
      `);
      
      const results = stmt.all();
      console.log(`WE-Daten abgerufen: ${results.length} Einträge gefunden`);
      
      if (results.length > 0) {
        console.log('Beispiel WE-Daten (erster Eintrag):', results[0]);
        
        // Konvertiere die Daten zum erwarteten Format
        const normalizedResults: WEDataPoint[] = results.map((row: any) => ({
          id: row.id,
          datum: row.datum,
          weAtrl: row.weAtrl,
          weManl: row.weManl
        }));

        console.log('Normalisierte WE-Daten (erster Eintrag):', normalizedResults[0]);
        return normalizedResults;
      }
      
      return [];
    } catch (error) {
      console.error('Fehler beim Abrufen der WE-Daten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Ruft die System-ATRL-Daten für das Diagramm ab
   * @returns Array von System-ATRL-Datenpunkten
   */
  public getSystemAtrlData(): SystemAtrlDataPoint[] {
    if (!this.db) {
      console.error('Keine Datenbankverbindung vorhanden');
      return [];
    }

    try {
      console.log('DatabaseService: Starte Abfrage der System-ATRL-Daten...');

      // Prüfe zuerst, ob die Tabelle existiert
      const tableCheck = this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='System'
      `).get();
      console.log('DatabaseService: System-Tabelle Check:', tableCheck);

      if (!tableCheck) {
        console.error('DatabaseService: Tabelle System existiert nicht');
        return [];
      }

      // Prüfe, ob Daten in der Tabelle vorhanden sind
      const countCheck = this.db.prepare(`
        SELECT COUNT(*) as count FROM System
      `).get() as { count: number };
      console.log('DatabaseService: Anzahl der Datensätze in System:', countCheck.count);

      if (countCheck.count === 0) {
        console.error('DatabaseService: Keine Daten in der Tabelle System');
        return [];
      }

      const stmt = this.db.prepare(`
        SELECT
          Datum,
          ninioSapAtrl,
          nioWitronAtrl,
          nioSiemensAtrl,
          nioProzessAtrl,
          nioSonstigesAtrl
        FROM System
        ORDER BY Datum ASC
      `);

      const results = stmt.all();
      console.log(`DatabaseService: ${results.length} System-ATRL-Datensätze abgerufen`);

      if (results.length > 0) {
        console.log('DatabaseService: Beispieldaten (erster Eintrag):', results[0]);
      }

      return results.map((row: any) => ({
        ...row,
        Datum: new Date(row.Datum).toLocaleDateString('de-DE'),
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der System-ATRL-Daten:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }
}

// Erstelle die Singleton-Instanz
const databaseService = new DatabaseService();

// Stelle sicher, dass die Methode existiert
if (!databaseService.getAblaengereiData) {
  console.error('FEHLER: getAblaengereiData existiert nicht auf der Datenbankinstanz!');
  console.log('Verfügbare Methoden:', Object.getOwnPropertyNames(Object.getPrototypeOf(databaseService)));
}

// Exportiere die Instanz
export { databaseService };
export default databaseService;